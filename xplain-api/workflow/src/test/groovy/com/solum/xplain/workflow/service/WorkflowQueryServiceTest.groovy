package com.solum.xplain.workflow.service

import com.solum.xplain.workflow.repository.ProcessExecutionRepository
import com.solum.xplain.workflow.repository.StepInstanceRepository
import com.solum.xplain.workflow.repository.WorkflowDataCacheService
import com.solum.xplain.workflow.value.WorkflowStatus
import spock.lang.Specification

class WorkflowQueryServiceTest extends Specification {
  ProcessExecutionRepository processExecutionRepository = Mock()
  StepInstanceRepository stepInstanceRepository = Mock()
  WorkflowDataCacheService workflowDataCacheService = Mock()
  WorkflowQueryService workflowQueryService = new WorkflowQueryService(processExecutionRepository, stepInstanceRepository, workflowDataCacheService)

  def "should get root process executions"() {
    given:
    def execIds = ["a", "b"]

    when:
    workflowQueryService.getRootProcessExecutions(execIds)

    then:
    1 * processExecutionRepository.getRootProcessExecutions(execIds)
  }

  def "should check if process exists"() {
    when:
    workflowQueryService.processExecutionExists("def", "key")

    then:
    1 * processExecutionRepository.existsByProcessIdAndBusinessKey("def", "key")
  }

  def "should return history step instances"() {
    when:
    workflowQueryService.getHistoricStepInstances("def", "key")

    then:
    1 * stepInstanceRepository.findByProcessIdAndBusinessKeyAndReportableTrueOrderByModifiedAtAsc("def", "key")
  }

  def "should return reportable step instances"() {
    when:
    workflowQueryService.getReportableStepInstances("def", ["key"], [WorkflowStatus.ACTIVE])

    then:
    1 * stepInstanceRepository.findByProcessIdAndBusinessKeyInAndReportableTrueAndStatusIn("def", ["key"], [WorkflowStatus.ACTIVE])
  }
}
