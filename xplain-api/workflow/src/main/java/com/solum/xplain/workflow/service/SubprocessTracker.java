package com.solum.xplain.workflow.service;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.solum.xplain.shared.datagrid.AtomicCounter;
import com.solum.xplain.shared.datagrid.DataGrid;
import com.solum.xplain.workflow.entity.StepInstance;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Tracks the completion of subprocesses so that parent steps can be marked as completed when all
 * their subprocesses are done.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SubprocessTracker {
  private final DataGrid dataGrid;
  private final Cache<String, AtomicCounter> counters =
      CacheBuilder.newBuilder().expireAfterAccess(1, TimeUnit.MINUTES).build();

  /**
   * Register a subprocess to be run for a parent step and return the sequence number (1-based).
   *
   * @param parentStep the parent step with subprocesses
   * @return sequence number allocated, or null if a sequence number could not be allocated
   */
  public int registerSubprocess(StepInstance<?> parentStep) {
    AtomicCounter counter;
    try {
      counter = subprocessCounter(parentStep);
    } catch (ExecutionException e) {
      throw new IllegalStateException("Failed to register subprocess", e);
    }
    int sequence = (int) counter.incrementAndGet();
    log.trace(
        "WF: {} ({}) - {} - registered subprocess {}",
        parentStep.getProcessId(),
        parentStep.getBusinessKey(),
        parentStep.getStepId(),
        sequence);
    return sequence;
  }

  /**
   * Reduce the number of subprocesses remaining for the parent step and return true if there are
   * none left to complete.
   *
   * @param parentStep the parent step with subprocesses
   * @return true if the parent step should now be marked completed
   */
  public boolean checkAfterSubprocessCompletes(StepInstance<?> parentStep) {
    AtomicCounter counter;
    try {
      counter = subprocessCounter(parentStep);
    } catch (ExecutionException e) {
      log.error(
          "WF: {} ({}) - {} - failed to get subprocess counter - relying on scheduled task for completion",
          parentStep.getProcessId(),
          parentStep.getBusinessKey(),
          parentStep.getStepId());
      return false;
    }
    long remaining = counter.decrementAndGet();
    log.trace(
        "WF: {} ({}) - {} - {} subprocesses remaining",
        parentStep.getProcessId(),
        parentStep.getBusinessKey(),
        parentStep.getStepId(),
        remaining);
    return remaining == 0L;
  }

  private AtomicCounter subprocessCounter(StepInstance<?> parentStep) throws ExecutionException {
    String key = parentStep.getId().toHexString();
    return counters.get(key, () -> dataGrid.getAtomicCounter(key));
  }

  /**
   * Gets the current count of registered subprocesses for the parent step without modifying the
   * count. Returns 0 if the counter hasn't been initialized or if there's an error retrieving it.
   *
   * @param parentStep the parent step with subprocesses
   * @return the current number of registered subprocesses, or 0 if none/error.
   */
  public long getRegisteredCount(StepInstance<?> parentStep) {
    try {
      return subprocessCounter(parentStep).get();
    } catch (ExecutionException e) {
      log.error(
          "WF: {} ({}) - {} - Failed to retrieve subprocess counter from DataGrid during cache load, assuming 0 registered. Underlying error: {}",
          parentStep.getProcessId(),
          parentStep.getBusinessKey(),
          parentStep.getStepId(),
          e.getMessage());
      return 0L;
    } catch (Exception e) {
      log.error(
          "WF: {} ({}) - {} - Unexpected error getting registered subprocess count. Assuming 0.",
          parentStep.getProcessId(),
          parentStep.getBusinessKey(),
          parentStep.getStepId(),
          e);
      return 0L;
    }
  }
}
