package com.solum.xplain.core.rules.jeasy

import com.fasterxml.jackson.databind.ObjectMapper
import org.jeasy.rules.api.Facts
import org.jeasy.rules.api.Rule
import org.jeasy.rules.api.Rules
import org.jeasy.rules.core.BasicRule
import spock.lang.Specification
import spock.lang.Unroll

class EasyRulesServiceTest extends Specification {
  static ArrayList<Rule> priorityRules = [
    new BasicRule("rule1", "low natural priority", 100),
    new BasicRule("rule2", "mid natural priority", 50),
    new BasicRule("rule3", "high natural priority", 0),
  ]
  static ArrayList<Rule> defaultPriorityRules = [
    new BasicRule("rule1", "2nd"),
    new BasicRule("rule2", "3rd"),
    new BasicRule("rule3", "1st"),
  ]

  def service = new EasyRulesService(new ObjectMapper())

  def "should create rule set in list order"(ArrayList<Rule> rules) {
    when:
    def ruleSet = service.createRuleSet(rules)

    then:
    ruleSet.size() == 3
    ruleSet.iterator().with {
      assert next().name == "rule1"
      assert next().name == "rule2"
      assert next().name == "rule3"
    }

    where:
    rules << [priorityRules, defaultPriorityRules]
  }

  def "should create rule set in natural order"(ArrayList<Rule> rules) {
    when:
    def ruleSet = service.createRuleSet(new TreeSet<Rule>(rules))

    then:
    ruleSet.size() == 3
    ruleSet.iterator().with {
      assert next().name == "rule3"
      assert next().name == "rule2"
      assert next().name == "rule1"
    }

    where:
    rules << [priorityRules]
  }

  def "should create rule set in explicit sorted order"(ArrayList<Rule> rules) {
    given:
    def sortedSet = new TreeSet<Rule>(Comparator.comparing(Rule::getDescription))
    sortedSet.addAll(rules)

    when:
    def ruleSet = service.createRuleSet(sortedSet)

    then:
    ruleSet.size() == 3
    ruleSet.iterator().with {
      assert next().name == "rule3"
      assert next().name == "rule1"
      assert next().name == "rule2"
    }

    where:
    rules << [priorityRules, defaultPriorityRules]
  }

  def "should create oneOf rule"() {
    given:
    Rule rule1 = Mock() {
      getName() >> "rule1"
      evaluate(_) >> rule1Matches
    }
    Rule rule2 = Mock() {
      getName() >> "rule2"
      evaluate(_) >> rule2Matches
    }
    Rule rule3 = Mock() {
      getName() >> "rule3"
      evaluate(_) >> rule3Matches
    }
    def rules = [rule1, rule2, rule3]
    def oneOf = service.oneOf(rules)

    when:
    oneOf.evaluate() && oneOf.execute()

    then:
    (expected == "rule1" ? 1 : 0) * rule1.execute(_)
    (expected == "rule2" ? 1 : 0) * rule2.execute(_)
    (expected == "rule3" ? 1 : 0) * rule3.execute(_)

    where:
    rule1Matches | rule2Matches | rule3Matches || expected
    true         | false        | false        || "rule1"
    true         | false        | true         || "rule1"
    true         | true         | false        || "rule1"
    true         | true         | true         || "rule1"
    false        | true         | false        || "rule2"
    false        | true         | true         || "rule2"
    false        | false        | true         || "rule3"
    false        | false        | false        || null
  }

  @Unroll
  def "should create predicated rule with predicate #predicateMatches and subordinate #subordinateMatches"() {
    given:
    Rule predicate = Mock() {
      getName() >> "predicate"
      evaluate(_) >> predicateMatches
    }
    Rule subordinate = Mock() {
      getName() >> "subordinate"
    }
    def predicated = service.predicated(predicate, [subordinate],1)

    when:
    predicated.evaluate() && predicated.execute()

    then:
    (predicateMatches ? 1 : 0) * subordinate.evaluate(_) >> subordinateMatches
    (predicateMatches ? 1 : 0) * predicate.execute(_)
    (predicateMatches && subordinateMatches ? 1 : 0) * subordinate.execute(_)

    where:
    predicateMatches | subordinateMatches
    true             | true
    true             | false
    false            | true
    false            | false
  }

  class InputFacts {
    String medal
  }

  enum Medal {
    GOLD, SILVER, BRONZE
  }

  def "should execute and return result of specified type"() {
    given:
    Rule rule = Mock() {
      getName() >> "rule1"
      evaluate(_) >> true
      execute(_ as Facts) >> { Facts f ->
        f.put("result", "winner")
        f.put("podiumPosition", Medal.valueOf(f.get("medal") as String))
      }
    }
    def rules = new Rules(rule)

    when:
    def inputFacts = new InputFacts(medal: "BRONZE")
    def result = service.execute(rules, inputFacts, Medal.class)

    then:
    result.isPresent()
    result.get() == Medal.BRONZE
  }
}
