package com.solum.xplain.core.classifiers

import static com.solum.xplain.core.classifiers.ClassifierUtils.valuesStream
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.SUPPORTED_CALENDARS_CLASSIFIER
import static com.solum.xplain.core.classifiers.Constants.EXPLICIT_CURRENCIES
import static com.solum.xplain.core.classifiers.CoreClassifiersProvider.ASSET_CLASSES
import static com.solum.xplain.core.classifiers.CoreClassifiersProvider.INSTRUMENT_PRICE_TYPES
import static com.solum.xplain.core.classifiers.CoreClassifiersProvider.INSTRUMENT_TYPES
import static com.solum.xplain.core.classifiers.CoreClassifiersProvider.MDK_BIDASK_TYPES
import static com.solum.xplain.core.classifiers.CoreClassifiersProvider.MD_VALUE_BIDASK_TYPES
import static com.solum.xplain.core.market.value.MdkProviderBidAskType.ASK_ONLY
import static com.solum.xplain.core.market.value.MdkProviderBidAskType.BID_ASK
import static com.solum.xplain.core.market.value.MdkProviderBidAskType.BID_ONLY
import static com.solum.xplain.core.market.value.MdkProviderBidAskType.MID_ONLY
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.ASK
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.BID
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.MID

import com.opengamma.strata.basics.index.IborIndex
import com.opengamma.strata.product.swap.type.FixedIborSwapConvention
import com.solum.xplain.core.curvegroup.instrument.CoreAssetClass
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType
import com.solum.xplain.core.portfolio.value.PortfolioCalculationType
import spock.lang.Specification

class ClassifiersControllerServiceTest extends Specification {

  def coreClassifiersProvider = new CoreClassifiersProvider()
  def classifiersAggegator = new ClassifiersAggregator([coreClassifiersProvider])
  def service = new ClassifiersControllerService(classifiersAggegator)

  def "should return all classifiers"() {
    when:
    def classifiers = service.classifiers

    then:
    classifiers.size() == 126
  }

  def "should return additional classifiers"() {
    when:
    def testId = "TEST"
    def additionalClassifiers = new ClassifiersProvider() {
        @Override
        List<Classifier> classifiers() {
          return [new Classifier(testId)]
        }

        @Override
        int sortOrder() {
          return 0
        }
      }
    def service = new ClassifiersControllerService(new ClassifiersAggregator([additionalClassifiers]))
    def testClassifier = service.classifiers.stream().filter({ c -> c.id == testId }).findAny().get()

    then:
    testClassifier.id == testId
  }

  def "should return correct cds quote conventions"() {
    when:
    def permissions = service.classifiers.stream().filter({ c -> c.id == "cdsQuoteConvention" })
    .findAny().get()

    then:
    permissions.values.size() == 3
    permissions.values.get(0).id == "PAR_SPREAD"
  }

  def "should return correct curve node types"() {
    when:
    def curveNodeTypes = service.classifiers.stream().filter({ c -> c.id == "curveNodeType" })
    .findAny().get()

    then:
    curveNodeTypes.values.size() == 17
    curveNodeTypes.values.get(0).id == "FixedIborSwap"
  }

  def "should return correct FRA settlement values"() {
    when:
    def fraSettlements = service.classifiers.stream().filter({ c -> c.id == "fraSettlement" })
    .findAny().get()

    then:
    fraSettlements.values.size() == 24
    fraSettlements.values.get(0).id == "1M"
    fraSettlements.values.get(23).id == "24M"
  }

  def "should return correct Serial future values"() {
    when:
    def serialFutures = service.classifiers.stream().filter({ c -> c.id == "serialFuture" })
    .findAny().get()

    then:
    serialFutures.values.size() == 36
    serialFutures.values.get(0).id == "2D+1"
    serialFutures.values.get(11).id == "2D+12"
    serialFutures.values.get(12).id == "1D+1"
    serialFutures.values.get(23).id == "1D+12"
    serialFutures.values.get(24).id == "0D+1"
    serialFutures.values.get(35).id == "0D+12"
  }

  def "should return correct currencies"() {
    when:
    def currencies = service.classifiers.stream().filter({ c -> c.id == "currency" })
    .findAny().get()

    then:
    currencies.values.size() == EXPLICIT_CURRENCIES.size()
    currencies.values.get(0).id == "XAU"
  }

  def "should return correct portfolio calculation types"() {
    when:
    def currencies = service.classifiers.stream().filter({ c ->
      c.id == "portfolioCalculationType"
    })
    .findFirst().get()

    then:
    currencies.values.size() == PortfolioCalculationType.values().size()
    currencies.values.get(0).id == "TRADES"
  }

  def "should return correct roll conventions"() {
    when:
    def rolls = service.classifiers.stream().filter({ c ->
      c.id == "rollConvention"
    })
    .findFirst().get()

    then:
    rolls.values.size() == 45
    rolls.values.get(0).id == "Day1"
  }

  def "should return correct fixed ibor swap conventions order"() {
    when:
    def swapConventions = CoreClassifiersProvider.swapConvention(valuesStream(FixedIborSwapConvention.extendedEnum()))

    then:
    def libor12m = swapConventions.values.stream().filter({ c -> c.id == "EUR-FIXED-1Y-EURIBOR-12M" })
    .findFirst().get()
    def libor3m = swapConventions.values.stream().filter({ c -> c.id == "EUR-FIXED-1Y-EURIBOR-3M" })
    .findFirst().get()

    swapConventions.values.indexOf(libor12m) > swapConventions.values.indexOf(libor3m)
  }

  def "should return correct ibor indices order"() {
    when:
    def swapConventions = CoreClassifiersProvider.swapConvention(valuesStream(IborIndex.extendedEnum()))

    then:
    def euribor2m = swapConventions.values.stream().filter({ c -> c.id == "EUR-EURIBOR-2M" })
    .findFirst().get()
    def euribor2w = swapConventions.values.stream().filter({ c -> c.id == "EUR-EURIBOR-2W" })
    .findFirst().get()

    swapConventions.values.indexOf(euribor2m) > swapConventions.values.indexOf(euribor2w)
  }

  def "should return correct dayCounts"() {
    when:
    def dayCounts = service.classifiers.stream().filter({ c -> c.id == "dayCount" })
    .findAny().get()

    then:
    dayCounts.values.size() == 12
  }

  def "should return correct IR instrument types"() {
    given:
    def expected = [
      [id: "IBOR_FIXING_DEPOSIT", name: "IborFixingDeposit"],
      [id: "TERM_DEPOSIT", name: "TermDeposit"],
      [id: "XCCY_IBOR_IBOR_SWAP", name: "XCCYIborIborSwap"],
      [id: "XCCY_OIS_OIS_SWAP", name: "XCCYOvernightOvernightSwap"],
      [id: "XCCY_IBOR_OIS_SWAP", name: "XCCYIborOvernightSwap"],
      [id: "XCCY_FIXED_OVERNIGHT_SWAP", name: "XCCYFixedOvernightSwap"],
      [id : "TERM_OIS_FIXING_DEPOSIT", name: "TermOISFixingDeposit"]
    ]

    when:
    def instruments = service.classifiers.stream().filter({ c -> c.id == "irInstruments" })
    .findFirst().get()

    then:
    instruments.values.size() == 16
    expected.each { match ->
      assert instruments.values.any { v -> v.id == match.id && v.name == match.name }
    }
  }

  def "should return correct market data RATE types"() {
    given:
    def expected = ["BOND_YIELD", "CAP_FLOOR_VOL", "XCCY_IBOR_IBOR_SWAP", "TERM_OIS_FIXING_DEPOSIT"]

    when:
    def rolls = service.classifiers.stream().filter({ c ->
      c.id == "marketDataRateInstruments"
    })
    .findFirst().get()

    then:
    rolls.values.size() == 20
    expected.forEach { type ->
      assert rolls.values.any { v ->
        v.id == type
      }
    }
  }

  def "should return correct market data FX types"() {
    when:
    def rolls = service.classifiers.stream().filter({ c ->
      c.id == "marketDataFxRateInstruments"
    })
    .findFirst().get()

    then:
    rolls.values.size() == 4
    rolls.values.get(0).id == "FX_RATE"
  }

  def "should return correct market data CREDIT types"() {
    when:
    def rolls = service.classifiers.stream().filter({ c ->
      c.id == "marketDataCreditInstruments"
    })
    .findFirst().get()

    then:
    rolls.values.size() == 3
    rolls.values.get(0).id == "CDS"
  }

  def "should return credit curve seniority"() {
    when:
    def seniorty = service.classifiers.stream().filter({ c -> c.id == "creditCurveSeniority" })
    .findAny().get()

    then:
    seniorty.values.size() == 6
    seniorty.values.get(0).id == "SECDOM"
    seniorty.values.get(5).id == "PREFT1"
  }

  def "should return correct index curve node conventions"() {
    when:
    def indexNodes = service.classifiers.stream().filter({ c -> c.id == "indexCurveNodeConventions" })
    .findAny().get()

    then:
    indexNodes.values.size() == 107
    indexNodes.values.get(0).id == "AED 1M"
    indexNodes.values.get(0).values.get(0).id == "IborFixingDeposit"
    indexNodes.values.get(0).values.get(0).values.get(0).id == "AED-EIBOR-1M"

    def krw = indexNodes.values.stream().filter({ x -> x.id == "KRW 13W" }).findFirst()
    krw.isPresent()
    krw.get().id == "KRW 13W"
    krw.get().values.get(0).id == "IborFixingDeposit"
    krw.get().values.get(0).values.get(0).id == "KRW-CD-13W"
    krw.get().values.get(1).id == "FixedIborSwap"
    krw.get().values.get(1).values.get(0).id == "KRW-FIXED-3M-CD-13W"

    def czk3m = indexNodes.values.stream().filter({ x -> x.id == "CZK 3M" }).findFirst()
    czk3m.isPresent()
    czk3m.get().id == "CZK 3M"
    czk3m.get().values.get(0).id == "IborFixingDeposit"
    czk3m.get().values.get(0).values.get(0).id == "CZK-PRIBOR-3M"
    czk3m.get().values.get(1).id == "FixedIborSwap"
    czk3m.get().values.get(1).values.get(0).id == "CZK-FIXED-1Y-PRIBOR-3M"
    czk3m.get().values.get(2).id == "ForwardRateAgreement"
    czk3m.get().values.get(2).values.get(0).id == "CZK-PRIBOR-3M"
  }

  def "should return correct tenor curve node conventions"() {
    when:
    def nodes = service.classifiers.stream().filter({ c -> c.id == "tenorBasisNodeConventions" })
    .findAny().get()

    then:
    nodes.values.size() == 36
    nodes.values.get(0).id == "AED 1M"
    nodes.values.get(0).values.get(0).id == "IborFixingDeposit"
    nodes.values.get(0).values.get(0).values.get(0).id == "AED-EIBOR-1M"
  }

  def "should return correct xccy curve node conventions"() {
    when:
    def nodes = service.classifiers.stream().filter({ c -> c.id == "xccyCurveNodeConventions" })
    .findAny().get()

    then:

    nodes.values.size() == 415
    nodes.values.get(0).id == "XAU/EUR"

    nodes.values.get(46).values.size() == 3
    nodes.values.get(46).values.get(0).id == "FxSwap"
    nodes.values.get(46).values.get(0).values.get(0).id == "EUR/GBP"
    nodes.values.get(46).values.get(1).id == "XCCYOvernightOvernightSwap"
    nodes.values.get(46).values.get(1).values.get(0).id == "GBP-SONIA-EUR-ESTR"

    nodes.values.stream().anyMatch { it.id == "SGD/CNH" }

    nodes.values.stream()
      .flatMap({ i ->
        i.getValues().stream()
      })
      .noneMatch({ i -> i.values.empty })
  }

  def "should return correct inflation curve node conventions"() {
    when:
    def nodes = service.classifiers.stream().filter({ c -> c.id == "inflationCurveNodeConventions" })
    .findAny().get()

    then:
    nodes.values.size() == 15
    nodes.values.get(0).id == "GB RPI"
    nodes.values.get(0).values.get(0).id == "FixedInflationSwap"
    nodes.values.get(0).values.get(0).values.get(0).id == "GBP-FIXED-ZC-GB-RPI"
    nodes.values.get(1).id == "EU AI CPI"
    nodes.values.get(1).values.get(0).id == "FixedInflationSwap"
    nodes.values.get(1).values.get(0).values.get(0).id == "EUR-FIXED-ZC-EU-AI-CPI"
    nodes.values.get(2).id == "EU EXT CPI"
    nodes.values.get(2).values.get(0).id == "FixedInflationSwap"
    nodes.values.get(2).values.get(0).values.get(0).id == "EUR-FIXED-ZC-EU-EXT-CPI"
  }

  def "should return correct inflation curve names"() {
    when:
    def nodes = service.classifiers.stream().filter({ c -> c.id == "inflationCurveNames" })
    .findAny().get()

    then:
    nodes.values.size() == 15
    nodes.values.get(0).id == "CH CPI"
    nodes.values.get(1).id == "EU AI CPI"
    nodes.values.get(2).id == "EU EXT CPI"
    nodes.values.get(3).id == "EU EXT CPI LCH"
    nodes.values.get(4).id == "FR EXT CPI"
    nodes.values.get(5).id == "FR EXT CPI LCH"
    nodes.values.get(6).id == "GB CPI"
    nodes.values.get(7).id == "GB CPI LCH"
    nodes.values.get(8).id == "GB HICP"
    nodes.values.get(9).id == "GB RPI"
    nodes.values.get(10).id == "GB RPI LCH"
    nodes.values.get(11).id == "GB RPIX"
    nodes.values.get(12).id == "JP CPI EXF"
    nodes.values.get(13).id == "US CPI U"
    nodes.values.get(14).id == "US CPI U LCH"
  }

  def "should return correct inflation curve indices"() {
    when:
    def nodes = service.classifiers.stream().filter({ c -> c.id == "inflationCurveIndices" })
    .findAny().get()

    then:
    nodes.values.size() == 10
    nodes.values.get(0).id == "CH-CPI"
    nodes.values.get(1).id == "EU-AI-CPI"
    nodes.values.get(2).id == "EU-EXT-CPI"
    nodes.values.get(3).id == "FR-EXT-CPI"
    nodes.values.get(4).id == "GB-CPI"
    nodes.values.get(5).id == "GB-HICP"
    nodes.values.get(6).id == "GB-RPI"
    nodes.values.get(7).id == "GB-RPIX"
    nodes.values.get(8).id == "JP-CPI-EXF"
    nodes.values.get(9).id == "US-CPI-U"
  }

  def "should return correct inflation indices conventions"() {
    when:
    def nodes = service.classifiers.stream().filter({ c -> c.id == "inflationIndexConventions" })
    .findAny().get()

    then:
    nodes.values.size() == 10
    nodes.values.get(0).id == "GB-RPI"
    nodes.values.get(0).values.get(0).id == "FixedInflationSwap"
    nodes.values.get(0).values.get(0).values.get(0).id == "GBP-FIXED-ZC-GB-RPI"
    nodes.values.get(1).id == "EU-AI-CPI"
    nodes.values.get(1).values.get(0).id == "FixedInflationSwap"
    nodes.values.get(1).values.get(0).values.get(0).id == "EUR-FIXED-ZC-EU-AI-CPI"
    nodes.values.get(2).id == "EU-EXT-CPI"
    nodes.values.get(2).values.get(0).id == "FixedInflationSwap"
    nodes.values.get(2).values.get(0).values.get(0).id == "EUR-FIXED-ZC-EU-EXT-CPI"
  }

  def "should return correct tenor curve names"() {
    when:
    def nodes = service.classifiers.stream().filter({ c -> c.id == "tenorBasisCurveNames" })
    .findAny().get()

    then:
    nodes.values.size() == 36
    nodes.values.get(0).id == "AED 12M"
  }

  def "should return correct index curve names"() {
    when:
    def nodes = service.classifiers.stream().filter({ c -> c.id == "irCurveNames" })
    .findAny().get()

    then:
    nodes.values.size() == 107
    nodes.values.get(0).id == "AED 12M"
  }

  def "should return correct xccy curve names"() {
    when:
    def nodes = service.classifiers.stream().filter({ c -> c.id == "xccyCurveNames" })
    .findAny().get()

    then:

    nodes.values.size() == 415
    nodes.values.get(0).id == "AUD/AED"
    nodes.values.stream().anyMatch { it.id == "SGD/CNH" }
    nodes.values.stream().anyMatch { it.id == "XAU/USD" }
  }

  def "should return correct bond curve names by ccy"() {
    when:
    def nodes = service.classifiers.stream().filter({ c -> c.id == "bondCurveNamesGroupedByCcy" })
    .findAny().get()

    then:
    nodes.values.size() == 4
    nodes.values[0].id == "EUR"
    nodes.values[0].values[0].id == "DEGT"
    nodes.values[1].id == "GBP"
    nodes.values[1].values[0].id == "UKGT"
    nodes.values[2].id == "JPY"
    nodes.values[2].values[0].id == "JPGT"
    nodes.values[3].id == "USD"
    nodes.values[3].values[0].id == "UST"
  }

  def "should return correct bond curve conventions"() {
    when:
    def nodes = service.classifiers.stream().filter({ c -> c.id == "bondCurveNodeConventions" })
    .findAny().get()

    then:
    nodes.values.size() == 4
    nodes.values.get(0).id == "UKGT"
    nodes.values.get(0).values.get(0).id == "BondYield"
  }

  def "should return correct fxSwapCurrencies"() {
    when:
    def nodes = service.classifiers.stream().filter({ c -> c.id == "fxSwapCurrencies" })
    .findAny().get()

    then:
    nodes.values.size() == 10
    nodes.values.get(0).id == "USD"
    nodes.values.get(0).values.size() == 41
    nodes.values.get(0).values.get(0).id == "USD/CAD"
    nodes.values.get(8).id == "SGD"
    nodes.values.get(9).id == "XAU"
    nodes.values.get(9).values.size() == 46
  }


  def "should return correct credit curve fixed rates"() {
    when:
    def fixedRates = service.classifiers.stream().filter({ c -> c.id == "creditFixedRate" })
    .findAny().get()

    then:
    fixedRates.values.size() == 5
    fixedRates.values.get(0).id == "0.0025"
    fixedRates.values.get(0).name == "25"
    fixedRates.values.get(4).id == "0.1"
    fixedRates.values.get(4).name == "1000"
  }

  def "should return correct curve configuration types"() {
    when:
    def nodes = service.classifiers.stream().filter({ c -> c.id == "curveConfigurationType" })
    .findAny().get()

    then:
    nodes.values.size() == 2
    nodes.values.get(0).id == "SINGLE"
  }

  def "should return correct valuation setting types"() {
    when:
    def nodes = service.classifiers.stream().filter({ c -> c.id == "valuationSettingType" })
    .findAny().get()

    then:
    nodes.values.size() == 2
    nodes.values.get(0).id == "BESPOKE"
  }

  def "should return correct assetClasses"() {
    when:
    def assets = service.classifiers.stream().filter({ c -> c.id == "allAssetClasses" })
    .findAny().get()

    then:
    assets.values.size() == 3
    assets.values.get(0).id == "RATES"
    assets.values.get(0).values.get(0).id == "IR_RATE"
    assets.values.get(1).id == "CREDIT"
    assets.values.get(1).values.size() == 3
    assets.values.get(1).values.get(0).id == "CDS"
    assets.values.get(1).values.get(1).id == "CREDIT_INDEX"
    assets.values.get(1).values.get(2).id == "CREDIT_INDEX_TRANCHE"
    assets.values.get(2).id == "FX"
  }

  def "should return correct asset class groups"() {
    when:
    def nodes = service.classifiers.stream().filter({ c -> c.id == "allAssetClassGroups" })
    .findAny().get()

    then:
    nodes.values.size() == 3
  }

  def "should return correct core asset class groups"() {
    when:
    def nodes = service.classifiers.stream().filter({ c -> c.id == "coreAssetClasses" })
    .findAny().get()

    then:
    nodes.values.size() == 3
  }

  def "should return correct calculation discounting types"() {
    when:
    def nodes = service.classifiers.stream().filter({ c -> c.id == "calculationDiscountingType" })
    .findAny().get()

    then:
    nodes.values.size() == 9
  }

  def "should return correct xva calculation discounting types"() {
    when:
    def nodes = service.classifiers.stream().filter({ c -> c.id == "xvaCalculationDiscountingType" })
    .findAny().get()

    then:
    nodes.values.size() == 8
  }

  def "should return correct volatility surface names"() {
    when:
    def volatilitySurfaces = service.classifiers.stream().filter({ c ->
      c.id == "volatilitySurfaceNames"
    }).findAny().get()

    then:
    volatilitySurfaces.values.size() == 54
    volatilitySurfaces.values.get(0).id == "AED 3M Vols"
    volatilitySurfaces.values.get(0).values.get(0).id == "AED-FIXED-1Y-EIBOR-3M"
  }

  def "should return correct fixing indices"() {
    when:
    def fixings = service.classifiers.stream().filter({ c -> c.id == "fixingProjectionIndices" })
    .findAny().get()

    then:
    fixings.values.size() == 102
    fixings.values.get(0).id == "AED-EIBOR-12M"
    fixings.values.get(81).id == "SEK-STIBOR-6M"
    fixings.values.get(89).id == "TRY-TLREF"
  }

  def "should return discounting currencies"() {
    when:
    def currencies = service.classifiers.stream().filter({ c -> c.id == "discountingCurrencies" })
    .findAny().get()

    then:
    currencies.values.size() == 8
    currencies.values[0].id == "USD"
  }

  def "should return XCCY fallback currencies"() {
    when:
    def currencies = service.classifiers.stream().filter({ c -> c.id == "xccyFallbackCurrencies" })
    .findAny().get()

    then:
    currencies.values.size() == 2
    currencies.values[0].id == "USD"
    currencies.values[1].id == "EUR"
  }

  def "should return doc clauses"() {
    when:
    def vals = service.classifiers.stream().filter({ c -> c.id == "creditDocClauses" })
    .findAny().get()

    then:
    vals.values.size() == 9
    vals.values[0].id == "MR14"
    vals.values[1].id == "MM14"
    vals.values[2].id == "XR14"
    vals.values[6].id == "XR"
    vals.values[7].id == "CR"
    vals.values[8].id == "UNDEFINED"
  }

  def "should return cds conventions"() {
    when:
    def vals = service.classifiers.stream().filter({ c -> c.id == "cdsCurveConvention" })
    .findAny().get()

    then:
    vals.values.size() == 4
    vals.values[0].id == "USD"
    vals.values[1].id == "EUR"
    vals.values[2].id == "GBP"
    vals.values[3].id == "JPY"
  }

  def "should return credit sectors"() {
    when:
    def vals = service.classifiers.stream().filter({ c -> c.id == "creditSectors" })
    .findAny().get()

    then:
    vals.values.size() == 14
  }

  def "should return convexityAdjTenors"() {
    when:
    def vals = service.classifiers.stream().filter({ c -> c.id == "convexityAdjTenors" })
    .findAny().get()

    then:
    vals.values.size() == 8
    vals.values[0].id == "1M"
  }

  def "should return convexityAdjCurveNames"() {
    when:
    def vals = service.classifiers.stream().filter({ c -> c.id == "convexityAdjCurveNames" })
    .findAny().get()

    then:
    vals.values.size() == 8
    vals.values[0].id == "AUD 3M"
  }

  def "should return Ibor indicies by currency"() {
    when:
    def vals = service.classifiers.stream().filter({ c -> c.id == "iborIndexByCurrency" })
    .findAny().get()

    then:
    vals.values.size() == 29
    vals.values[0].id == "AED"
    vals.values[0].values[0].id == "AED-EIBOR-12M"
  }

  def "should return Cap Floor trade Ibor indicies by currency"() {
    when:
    def vals = service.classifiers.stream().filter({ c -> c.id == "capFloorTradeIborIndexByCcy" })
    .findAny().get()

    then:
    vals.values.size() == 27
    vals.values[0].id == "AED"
    vals.values[0].values[0].id == "AED-EIBOR-3M"
    vals.values[12].id == "JPY"
    vals.values[12].values.size() == 3
    vals.values[12].values[0].id == "JPY-LIBOR-1M"
    vals.values[12].values[1].id == "JPY-LIBOR-3M"
    vals.values[12].values[2].id == "JPY-LIBOR-6M"
  }

  def "should return Swaption trade Ibor indicies by currency"() {
    when:
    def vals = service.classifiers.stream().filter({ c -> c.id == "swaptionTradeIborIndexByCcy" })
    .findAny().get()

    then:
    vals.values.size() == 27
    vals.values[0].id == "AED"
    vals.values[0].values[0].id == "AED-EIBOR-3M"
    vals.values[1].id == "AUD"
    vals.values[1].values[0].id == "AUD-BBSW-3M"
    vals.values[1].values[1].id == "AUD-BBSW-6M"
  }

  def "should return Overnight indicies by currency"() {
    when:
    def vals = service.classifiers.stream().filter({ c -> c.id == "overnightIndexByCurrency" })
    .findAny().get()

    then:
    vals.values.size() == 29
    vals.values[0].id == "AUD"
    vals.values[0].values[0].id == "AUD-AONIA"
    vals.values[19].id == "NOK"
    vals.values[19].values[0].id == "NOK-NOWA"
  }

  def "should return Inflation indicies by currency"() {
    when:
    def vals = service.classifiers.stream().filter({ c -> c.id == "inflationIndexByCurrency" })
    .findAny().get()

    then:
    vals.values.size() == 5
    vals.values[0].id == "CHF"
    vals.values[0].values[0].id == "CH-CPI"
    vals.values[1].id == "EUR"
    vals.values[1].values[0].id == "EU-AI-CPI"
    vals.values[1].values[1].id == "EU-EXT-CPI"
    vals.values[1].values[2].id == "FR-EXT-CPI"
    vals.values[2].id == "GBP"
    vals.values[2].values[0].id == "GB-CPI"
    vals.values[2].values[1].id == "GB-HICP"
    vals.values[2].values[2].id == "GB-RPI"
    vals.values[2].values[3].id == "GB-RPIX"
    vals.values[3].id == "JPY"
    vals.values[3].values[0].id == "JP-CPI-EXF"
    vals.values[4].id == "USD"
    vals.values[4].values[0].id == "US-CPI-U"
  }

  def "should return correct curve configuration type"() {
    when:
    def types = service.classifiers.stream().filter({ c -> c.id == "curveConfigurationType" })
    .findAny().get()

    then:
    types.values.size() == 2
    types.values.get(0).name == "Single"
    types.values.get(0).id == "SINGLE"
    types.values.get(1).name == "FX vs non-FX Instruments"
    types.values.get(1).id == "FX_V_IRS"
  }

  def "should return correct productTypeGroup"() {
    when:
    def types = service.classifiers.stream().filter({ c -> c.id == "productTypeGroup" })
    .findAny().get()

    then:
    types.values.size() == 3
    types.values.get(0).id == "RATES"
    types.values.get(0).values.get(0).id == "CAP_FLOOR"
  }

  def "should return correct counterparty types"() {
    when:
    def types = service.classifiers.stream().filter({ c -> c.id == "counterpartyType" })
    .findAny().get()

    then:
    types.values.size() == 2
    types.values.get(0).id == "CLEARED"
    types.values.get(1).id == "BILATERAL"
  }

  def "should return correct valuation statuses"() {
    when:
    def statuses = service.classifiers.stream().filter({ c -> c.id == "valuationStatus" })
    .findAny().get()

    then:
    statuses.values.size() == 2
    statuses.values.get(0).id == "ERROR"
    statuses.values.get(1).id == "OK"
  }

  def "should return cds indices"() {
    when:
    def types = service.classifiers.stream().filter({ c -> c.id == "cdsIndex" })
    .findAny().get()

    then:
    types.values.size() == 32
    types.values.get(0).id == "CDX_EM"
    types.values.get(0).name == "CDX EM"

    types.values.get(31).id == "ABX_HE_BBB_MINUS"
    types.values.get(31).name == "ABX HE BBB-"
  }

  def "should return credit curve types"() {
    when:
    def types = service.classifiers.stream().filter({ c -> c.id == "creditType" })
    .findAny().get()

    then:
    types.values.size() == 3
    types.values.get(0).id == "CDS"
    types.values.get(0).name == "CDS"
    types.values.get(1).id == "CREDIT_INDEX"
    types.values.get(1).name == "Credit Index"
    types.values.get(2).id == "CREDIT_INDEX_TRANCHE"
    types.values.get(2).name == "Credit Index Tranche"
  }

  def "should return volatility swap conventions"() {
    when:
    def conventions = service.classifiers.stream().filter({ c -> c.id == "volatilitySwapConvention" })
    .findAny().get()
    then:
    conventions.values.size() == 54
  }

  def "should return stripping types"() {
    when:
    def types = service.classifiers.stream().filter({ c -> c.id == "strippingType" })
    .findAny().get()
    then:
    types.values.size() == 2
    types.values[0].id == "OIS"
    types.values[0].name == "OIS (Dual)"
    types.values[1].id == "LIBOR"
    types.values[1].name == "Single"
  }

  def "should return credit frequencies"() {
    when:
    def creditFrequencies = service.classifiers.stream().filter({ c -> c.id == "creditFrequency" })
    .findAny().get()
    then:
    creditFrequencies.values.size() == 19
    creditFrequencies.values[0].id == "1W"
    creditFrequencies.values[18].id == "TERM"
  }

  def "should return minimum node gaps"() {
    when:
    def minNodeGaps = service.classifiers.stream().filter({ c -> c.id == "minNodeGap" })
    .findAny().get()
    then:
    minNodeGaps.values.size() == 7
    minNodeGaps.values[0].id == "1D"
    minNodeGaps.values[6].id == "1W"
  }

  def "should return trade calculation types"() {
    when:
    def calculationType = service.classifiers.stream().filter({ c -> c.id == "tradeCalculationType" })
    .findAny().get()
    then:
    calculationType.values.size() == 5
    calculationType.values[0].id == "FIXED"
    calculationType.values[3].id == "OVERNIGHT"
    calculationType.values[4].id == "TERM_OVERNIGHT"
  }

  def "should return caplet valuation models"() {
    when:
    def models = service.classifiers.stream().filter({ c -> c.id == "capletValuationModel" })
    .findAny().get()
    then:
    models.values.size() == 2
    models.values[0].id == "NORMAL"
    models.values[0].name == "Normal"
    models.values[1].id == "BLACK"
    models.values[1].name == "Lognormal"
  }

  def "should return volatility types"() {
    when:
    def types = service.classifiers.stream().filter({ c -> c.id == "volatilityType" })
    .findAny().get()
    then:
    types.values.size() == 3
    types.values[0].id == "ATM_ONLY"
    types.values[0].name == "ATM Only"
    types.values[1].id == "STRIKE"
    types.values[1].name == "Absolute Strike"
    types.values[2].id == "MONEYNESS"
    types.values[2].name == "Moneyness"
  }

  def "should return swaption settlement types"() {
    when:
    def models = service.classifiers.stream().filter({ c -> c.id == "swaptionSettlementType" })
    .findAny().get()
    then:
    models.values.size() == 2
    models.values[0].id == "CASH"
    models.values[0].name == "Cash (PAR_YIELD)"
    models.values[1].id == "PHYSICAL"
    models.values[1].name == "Physical"
  }

  def "should return correct default curve value types"() {
    when:
    def permissions = service.classifiers.stream().filter({ c -> c.id == "defaultCurveYInterpolationMethod" })
    .findAny().get()

    then:
    permissions.values.size() == 3
    permissions.values.get(0).id == "ZeroRate"
    permissions.values.get(0).name == "Zero Rate"
    permissions.values.get(1).id == "ForwardRate"
    permissions.values.get(1).name == "Forward Rate"
    permissions.values.get(2).name == "Discount Factor"
    permissions.values.get(2).id == "DiscountFactor"
  }

  def "should return correct curve value types"() {
    when:
    def permissions = service.classifiers.stream().filter({ c -> c.id == "inflationCurveYInterpolationMethod" })
    .findAny().get()

    then:
    permissions.values.size() == 1
    permissions.values.get(0).id == "PriceIndex"
    permissions.values.get(0).name == "Price Index"
  }

  def "should return curve interpolators"() {
    when:
    def models = service.classifiers.stream().filter({ c -> c.id == "curveInterpolator" })
    .findAny().get()
    then:
    models.values.size() == 16
    models.values[0].id == "DoubleQuadratic"
    models.values[1].id == "Linear"
    models.values[2].id == "LogLinear"
    models.values[3].id == "LogNaturalSplineDiscountFactor"
    models.values[4].id == "LogNaturalSplineMonotoneCubic"
    models.values[5].id == "NaturalCubicSpline"
    models.values[6].id == "NaturalSpline"
    models.values[7].id == "NaturalSplineNonnegativityCubic"
    models.values[8].id == "PiecewiseCubicHermiteMonotonicity"
    models.values[9].id == "ProductLinear"
    models.values[10].id == "ProductNaturalSpline"
    models.values[11].id == "ProductNaturalSplineMonotoneCubic"
    models.values[12].id == "SquareLinear"
    models.values[13].id == "StepLower"
    models.values[14].id == "StepUpper"
    models.values[15].id == "TimeSquare"
  }

  def "should return curve extrapolators"() {
    when:
    def models = service.classifiers.stream().filter({ c -> c.id == "curveExtrapolator" })
    .findAny().get()
    then:
    models.values.size() == 10
    models.values[0].id == "DiscountFactorLinearRightZeroRateCurve"
    models.values[1].id == "DiscountFactorQuadraticLeftZeroRate"
    models.values[2].id == "Exception"
    models.values[3].id == "Exponential"
    models.values[4].id == "Flat"
    models.values[5].id == "Interpolator"
    models.values[6].id == "Linear"
    models.values[7].id == "LogLinear"
    models.values[8].id == "ProductLinear"
    models.values[9].id == "QuadraticLeft"
  }

  def "should return surface extrapolators"() {
    when:
    def models = service.classifiers.stream().filter({ c -> c.id == "surfaceExtrapolator" })
    .findAny().get()
    then:
    models.values.size() == 8
    models.values[0].id == "Exception"
    models.values[1].id == "Exponential"
    models.values[2].id == "Flat"
    models.values[3].id == "Interpolator"
    models.values[4].id == "Linear"
    models.values[5].id == "LogLinear"
    models.values[6].id == "ProductLinear"
    models.values[7].id == "QuadraticLeft"
  }

  def "should return business day adjustment types"() {
    when:
    def models = service.classifiers.stream().filter({ c -> c.id == "businessDayAdjustmentType" })
    .findAny().get()
    then:
    models.values.size() == 2
    models.values[0].id == "ACCRUAL_AND_PAYMENT"
    models.values[0].name == "Accrual and Payment Dates"
    models.values[1].id == "PAYMENT_ONLY"
    models.values[1].name == "Payment Dates Only"
  }

  def "should return call put types"() {
    when:
    def models = service.classifiers.stream().filter({ c -> c.id == "callPutType" })
    .findAny().get()
    then:
    models.values.size() == 2
    models.values[0].id == "CALL"
    models.values[1].id == "PUT"
  }

  def "should return position long short types"() {
    when:
    def results = service.classifiers.stream().filter({ c -> c.id == "fxLongShort" })
    .findAny().get()
    then:
    results.values.size() == 2
    results.values[0].id == "LONG"
    results.values[1].id == "SHORT"
  }


  def "should have expected supported calendars"() {
    setup:
    def expectedCalendars = [
      "ARBA",
      "AUSY",
      "BRBD",
      "CNBE",
      "CATO",
      "CLSA",
      "COBO",
      "CRSJ",
      "CZPR",
      "DKCO",
      "GBLO",
      "HUBU",
      "HKHK",
      "HRZA",
      "IDJA",
      "ILTA",
      "JPTO",
      "KRSE",
      "KWKC",
      "MYKL",
      "MXMC",
      "NOOS",
      "NZAU",
      "OMMU",
      "PELI",
      "PLWA",
      "QADO",
      "RUMO",
      "INMU",
      "ROBU",
      "SARI",
      "ZAJO",
      "SGSI",
      "SEST",
      "CHZU",
      "TWTA",
      "EUTA",
      "THBA",
      "TRIS",
      "UYMO",
      "AEDU",
      "USNY",
      "USGS",
      "UGKA",
      "PHMA",
    ]

    when:
    def results = service.classifiers.find({ it -> it.id == SUPPORTED_CALENDARS_CLASSIFIER })

    then:
    var actualCalendars = results.values.stream().map({ it -> it.getId() }).toList()
    actualCalendars.toSorted() == expectedCalendars.sort()
  }

  def "should have expected loan day counts"() {
    setup:
    def expectedDayCounts = [
      "1/1",
      "30/360 ISDA",
      "30E/360",
      "30E/360 ISDA",
      "30U/360",
      "Act/360",
      "Act/365 Actual",
      "Act/365F",
      "Act/365L",
      "Act/Act ICMA",
      "Act/Act ISDA",
      "Act/Act Year"
    ]

    when:
    def results = service.classifiers.find({ it -> it.id == "loanDayCounts" })

    then:
    var actualDayCounts = results.values.stream().map({ it -> it.getId() }).toList()
    actualDayCounts.toSorted() == expectedDayCounts.sort()
  }

  def "should have expected JP loan day counts"() {
    setup:
    def expectedDayCounts = [
      "1/1",
      "30/360 ISDA",
      "30E/360",
      "30E/360 ISDA",
      "30U/360",
      "Act/360",
      "Act/365 Actual",
      "Act/365F",
      "Act/365L",
      "Act/Act ISDA",
      "Act/Act Year"
    ]

    when:
    def results = service.classifiers.find({ it -> it.id == "jpLoanDayCounts" })

    then:
    var actualDayCounts = results.values.stream().map({ it -> it.getId() }).toList()
    actualDayCounts.toSorted() == expectedDayCounts.sort()
  }

  def "should return MDK bidAsk types"() {
    when:
    def models = service.classifiers.stream().filter({ c -> c.id == MDK_BIDASK_TYPES })
    .findAny().get()
    then:
    models.values.size() == 4
    models.values[0].id == BID_ONLY.name()
    models.values[0].name == "Bid Only"
    models.values[1].id == MID_ONLY.name()
    models.values[1].name == "Mid Only"
    models.values[2].id == ASK_ONLY.name()
    models.values[2].name == "Ask Only"
    models.values[3].id == BID_ASK.name()
    models.values[3].name == "Bid/Ask"
  }

  def "should return MD value bidAsk types"() {
    when:
    def models = service.classifiers.stream().filter({ c -> c.id == MD_VALUE_BIDASK_TYPES })
    .findAny().get()
    then:
    models.values.size() == 3
    models.values[0].id == BID.name()
    models.values[0].name == "Bid"
    models.values[1].id == MID.name()
    models.values[1].name == "Mid"
    models.values[2].id == ASK.name()
    models.values[2].name == "Ask"
  }

  def "should return instrument price types"() {
    when:
    def models = service.classifiers.stream().filter({ c -> c.id == INSTRUMENT_PRICE_TYPES })
    .findAny().get()
    then:
    models.values.size() == 3
    models.values[0].id == InstrumentPriceType.BID_PRICE.name()
    models.values[0].name == "Bid"
    models.values[1].id == InstrumentPriceType.MID_PRICE.name()
    models.values[1].name == "Mid"
    models.values[2].id == InstrumentPriceType.ASK_PRICE.name()
    models.values[2].name == "Ask"
  }

  def "should have correct FX currencies"() {
    def expectedCurrencies = [
      "XAU",
      "EUR",
      "GBP",
      "AUD",
      "NZD",
      "USD",
      "CAD",
      "CHF",
      "JPY",
      "SGD",
      "AED",
      "ARS",
      "BRL",
      "CNH",
      "CLP",
      "CNY",
      "COP",
      "CRC",
      "CZK",
      "DKK",
      "HKD",
      "HRK",
      "HUF",
      "IDR",
      "ILS",
      "INR",
      "KRW",
      "KWD",
      "MXN",
      "MXV",
      "MYR",
      "NOK",
      "OMR",
      "PEN",
      "PLN",
      "PHP",
      "QAR",
      "RON",
      "RUB",
      "SAR",
      "SEK",
      "THB",
      "TRY",
      "TWD",
      "UGX",
      "UYU",
      "ZAR"
    ]

    when:
    def results = service.classifiers.find({ it -> it.id == "fxTradeCurrency" })

    then:
    var actualDayCounts = results.values.stream().map({ it -> it.getId() }).toList()
    actualDayCounts == expectedCurrencies
  }

  def "should return credit index tranches"() {
    when:
    def models = service.classifiers.stream().filter({ c -> c.id == "creditIndexTranche" })
    .findAny().get()
    then:
    models.values.size() == 14
    models.values[0].id == "0-10"
    models.values[0].values.size() == 1
    models.values[0].values[0].id == "ITRAXX_EUR_XOVER"
  }

  def "should return pricing slots"() {
    when:
    def models = service.classifiers.stream().filter({ c -> c.id == "pricingSlots" })
    .findAny().get()
    then:
    models.values.size() == 26
    models.values[0].id == "LDN_1200"
  }

  def "should return sla deadlines"() {
    when:
    def models = service.classifiers.stream().filter({ c -> c.id == "slaDeadlines" })
    .findAny().get()
    then:
    models.values.size() == 15
    models.values[0].id == "OTHER"
  }

  def "should return instrument types"() {
    when:
    def models = service.classifiers.stream().filter({ c -> c.id == INSTRUMENT_TYPES })
    .findAny().get()
    then:
    with (models.values) {
      assert size() == CoreInstrumentType.values().length
      assert it[0].id == "IBOR_FIXING_DEPOSIT"
      assert it[0].name == "IborFixingDeposit"
    }
  }

  def "should return ungrouped asset classes"() {
    when:
    def models = service.classifiers.stream().filter({ c -> c.id == ASSET_CLASSES })
    .findAny().get()
    then:
    with (models.values) {
      assert size() == CoreAssetClass.values().length
      assert it[0].id == "IR_RATE"
      assert it[0].name == "IR Rates"
    }
  }

  def "should return exposure shift types"() {
    when:
    def models = service.classifiers.stream().filter({ c -> c.id == "exposureShiftType" })
    .findAny().get()

    then:
    with(models.values) {
      assert size() == 2
      assert it[0].id == "ABSOLUTE"
      assert it[0].name == "Absolute"
      assert it[1].id == "RELATIVE"
      assert it[1].name == "Relative"
    }
  }
}
