package com.solum.xplain.core.ccyexposure.csv;

import com.solum.xplain.core.common.csv.ItemKey;
import java.time.LocalDate;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@RequiredArgsConstructor
public class CashflowDateItemKey implements ItemKey {
  private final String ccyExposureName;
  private final LocalDate date;

  @Override
  public String getIdentifier() {
    return ccyExposureName + "-" + date.toString();
  }

  @Override
  public String getEntityTypeName() {
    return "Cashflow";
  }

  @Override
  public String getParentEntityTypeName() {
    return "Ccy Exposure";
  }
}
