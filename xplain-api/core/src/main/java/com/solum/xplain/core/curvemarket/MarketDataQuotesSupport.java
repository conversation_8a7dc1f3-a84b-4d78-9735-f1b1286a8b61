package com.solum.xplain.core.curvemarket;

import static java.util.Collections.emptyMap;

import com.solum.xplain.core.curvemarket.datasource.MarketDataSource;
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceFactory;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueView;
import java.util.Map;
import java.util.function.BiFunction;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class MarketDataQuotesSupport {

  private final MarketDataSourceFactory marketDataSourceFactory;

  public Map<String, CalculationMarketValueFullView> getFullQuotes(
      CurveConfigMarketStateForm stateForm) {
    return quotes(
        stateForm,
        (source, key) -> source.provideMarketData(key, key.getMarketDataSource().getToFullView()));
  }

  public Map<String, CalculationMarketValueView> getQuotes(CurveConfigMarketStateForm stateForm) {
    return quotes(
        stateForm,
        (source, key) -> source.provideMarketData(key, key.getMarketDataSource().getToView()));
  }

  private <T> Map<String, T> quotes(
      CurveConfigMarketStateForm stateForm,
      BiFunction<MarketDataSource, MarketDataExtractionParams, Map<String, T>> quoteFn) {
    return stateForm
        .toKey()
        .map(SingleDateMarketDataExtractionParams::mdParamsWithoutCurves)
        .flatMap(
            key ->
                marketDataSourceFactory
                    .get(key.getMarketDataSource())
                    .toOptional()
                    .map(provider -> quoteFn.apply(provider, key)))
        .orElse(emptyMap());
  }
}
