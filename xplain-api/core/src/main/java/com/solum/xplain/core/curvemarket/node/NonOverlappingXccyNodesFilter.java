package com.solum.xplain.core.curvemarket.node;

import static com.solum.xplain.core.curvegroup.conventions.ClearingHouse.NONE;
import static com.solum.xplain.core.error.Error.CALIBRATION_WARNING;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.market.curve.CurveNodeDateOrder;
import com.solum.xplain.core.classifiers.CurveNodeTypes;
import com.solum.xplain.core.curvegroup.curve.entity.CurveNode;
import com.solum.xplain.core.error.ErrorItem;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class NonOverlappingXccyNodesFilter {

  public static final String DROPPED_NODE_WARNING =
      "Market tenor node with key %s was dropped since it clashes with the next node";

  public static ValidNodesFilter ofValidNodes(
      LocalDate valuationDate,
      ReferenceData referenceData,
      CurveNodeDateOrder nodeDateOrder,
      Consumer<List<ErrorItem>> warningsConsumer) {
    return new ValidNodesFilter() {
      @Override
      public <T> List<T> filterNodes(List<NodeInstrumentWrapper<T>> nodes) {
        var filteredNodes = new ArrayList<T>(nodes.size());
        for (int i = 0; i < nodes.size(); i++) {
          if (shouldKeepNode(nodes, i, nodeDateOrder, valuationDate, referenceData)) {
            filteredNodes.add(nodes.get(i).getNode());
          } else {
            warningsConsumer.accept(
                ImmutableList.of(
                    CALIBRATION_WARNING.entity(
                        String.format(
                            DROPPED_NODE_WARNING, nodes.get(i).getDefinition().getKey()))));
          }
        }
        return ImmutableList.copyOf(filteredNodes);
      }
    };
  }

  private static boolean isFxWeekNode(CurveNode curveNode) {
    var valid = List.of("1W", "2W", "3W", "4W"); // don't need 1M as that's the next node
    return CurveNodeTypes.FX_SWAP_NODE.equalsIgnoreCase(curveNode.getType())
        && valid.contains(curveNode.getPeriod());
  }

  private static <T> boolean shouldKeepNode(
      List<NodeInstrumentWrapper<T>> nodes,
      int index,
      CurveNodeDateOrder order,
      LocalDate valuationDate,
      ReferenceData refData) {
    if (index < nodes.size() - 1
        && nodes.get(index).getNode() instanceof CurveNode curveNode
        && isFxWeekNode(curveNode)) {
      var currDate = curveNode.date(false, NONE, valuationDate, order, refData);
      var nextNode = (CurveNode) nodes.get(index + 1).getNode();
      var nextDate = nextNode.date(false, NONE, valuationDate, order, refData);
      return nextDate.isAfter(currDate);
    }
    return true;
  }
}
