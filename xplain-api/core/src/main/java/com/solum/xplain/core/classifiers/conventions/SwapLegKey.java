package com.solum.xplain.core.classifiers.conventions;

import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.basics.index.PriceIndex;
import com.solum.xplain.core.portfolio.value.CalculationType;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import lombok.Data;

@Data
public class SwapLegKey {
  private final CalculationType type;
  private final String index;

  public static SwapLegKey fixedLeg() {
    return new SwapLegKey(CalculationType.FIXED, null);
  }

  public static SwapLegKey overnight(OvernightIndex index) {
    return new SwapLegKey(CalculationType.OVERNIGHT, index.getName());
  }

  public static SwapLegKey inflation(PriceIndex index) {
    return new SwapLegKey(CalculationType.INFLATION, index.getName());
  }

  public static SwapLegKey ibor(IborIndex index) {
    return new SwapLegKey(CalculationType.IBOR, index.getName());
  }

  public static SwapLegKey termOvernight(OvernightTermIndex index) {
    return new SwapLegKey(CalculationType.TERM_OVERNIGHT, index.getName());
  }

  public static SwapLegKey fromLeg(Leg leg) {
    return new SwapLegKey(leg.getCalculationType(), leg.getIndex());
  }
}
