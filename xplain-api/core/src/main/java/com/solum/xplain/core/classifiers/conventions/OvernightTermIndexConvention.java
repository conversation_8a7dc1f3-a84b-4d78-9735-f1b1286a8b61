package com.solum.xplain.core.classifiers.conventions;

import static com.solum.xplain.core.utils.FrequencyUtils.toStringNoPrefix;

import com.opengamma.strata.basics.schedule.Frequency;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.jspecify.annotations.NullMarked;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@NullMarked
public class OvernightTermIndexConvention {
  private final String name;
  private final String tenor;
  private final String currency;
  private final String dayCount;
  private final String fixingCalendar;
  private final int fixingDateOffset;

  public static OvernightTermIndexConvention newOf(OvernightTermIndex overnightTermIndex) {
    return new OvernightTermIndexConvention(
        overnightTermIndex.getName(),
        toStringNoPrefix(Frequency.of(overnightTermIndex.getTenor().getPeriod())),
        ConventionMapper.INSTANCE.map(overnightTermIndex.getCurrency()),
        ConventionMapper.INSTANCE.map(overnightTermIndex.getDayCount()),
        ConventionMapper.INSTANCE.map(overnightTermIndex.getFixingCalendar()),
        overnightTermIndex.getFixingDateOffset().getDays());
  }
}
