package com.solum.xplain.core.classifiers.discounting;

import com.opengamma.strata.basics.currency.Currency;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import org.springframework.stereotype.Component;

@Component
public class ValidIndexBasedDiscountCurrenciesValidator
    implements ConstraintValidator<ValidIndexBasedDiscountCurrencies, List<String>> {

  @Override
  public boolean isValid(List<String> currencies, ConstraintValidatorContext context) {
    if (currencies == null) {
      return true;
    }

    var discountCurrencies = indexBasedDiscountCurrencies(currencies);
    if (hasDuplicateDiscountCurrencies(discountCurrencies, Currency.EUR)) {
      addError(context, "MultipleEURDiscountingCurrencies");
      return false;
    }

    if (hasDuplicateDiscountCurrencies(discountCurrencies, Currency.USD)) {
      addError(context, "MultipleUSDDiscountingCurrencies");
      return false;
    }

    return true;
  }

  private List<IndexBasedDiscountCurrency> indexBasedDiscountCurrencies(List<String> currencies) {
    return currencies.stream()
        .map(IndexBasedDiscountCurrencies::get)
        .filter(Objects::nonNull)
        .toList();
  }

  private boolean hasDuplicateDiscountCurrencies(
      List<IndexBasedDiscountCurrency> discountingCurrencies, Currency currency) {
    return discountingCurrencies.stream()
            .map(IndexBasedDiscountCurrency::getCurrency)
            .filter(Predicate.isEqual(currency))
            .count()
        > 1;
  }

  private void addError(ConstraintValidatorContext context, String messageTemplateSuffix) {
    context.disableDefaultConstraintViolation();
    context
        .buildConstraintViolationWithTemplate(
            String.format("{ValidDiscountingCurrencies.%s}", messageTemplateSuffix))
        .addConstraintViolation();
  }
}
