package com.solum.xplain.core.classifiers.csv;

import static com.solum.xplain.core.common.csv.CsvColumn.textObject;
import static com.solum.xplain.core.portfolio.csv.DayCountCsvUtils.toExportLabel;

import com.opengamma.strata.product.swap.type.FixedInflationSwapConvention;
import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import java.util.List;
import org.springframework.core.io.ByteArrayResource;

public class FixedInflationSwapConventionMapper extends CsvMapper<FixedInflationSwapConvention> {

  private static final List<CsvColumn<FixedInflationSwapConvention>> COLUMNS =
      List.of(
          textObject("name", FixedInflationSwapConvention::getName),
          textObject("spotDateOffset", FixedInflationSwapConvention::getSpotDateOffset),
          textObject("fixedLeg.currency", x -> x.getFixedLeg().getCurrency()),
          textObject(
              "fixedLeg.dayCount", x -> toExportLabel(x.getFixedLeg().getDayCount().getName())),
          textObject("fixedLeg.accrualFreq", x -> x.getFixedLeg().getAccrualFrequency()),
          textObject("fixedLeg.accrual", x -> x.getFixedLeg().getAccrualMethod()),
          textObject("fixedLeg.accrualBDA", x -> x.getFixedLeg().getAccrualBusinessDayAdjustment()),
          textObject(
              "fixedLeg.startDateBDA", x -> x.getFixedLeg().getStartDateBusinessDayAdjustment()),
          textObject("fixedLeg.endDateBDA", x -> x.getFixedLeg().getEndDateBusinessDayAdjustment()),
          textObject("fixedLeg.stubConv", x -> x.getFixedLeg().getStubConvention()),
          textObject("fixedLeg.rollConv", x -> x.getFixedLeg().getRollConvention()),
          textObject("fixedLeg.payFreq", x -> x.getFixedLeg().getPaymentFrequency()),
          textObject("fixedLeg.payDateOffset", x -> x.getFixedLeg().getPaymentDateOffset()),
          textObject("fixedLeg.compounding", x -> x.getFixedLeg().getCompoundingMethod()),
          textObject("floatingLeg.index", x -> x.getFloatingLeg().getIndex()),
          textObject("floatingLeg.currency", x -> x.getFloatingLeg().getCurrency()),
          textObject(
              "floatingLeg.calculation", x -> x.getFloatingLeg().getIndexCalculationMethod()),
          textObject("floatingLeg.lag", x -> x.getFloatingLeg().getLag()),
          textObject("floatingLeg.notionalExc", x -> x.getFloatingLeg().isNotionalExchange()));

  private FixedInflationSwapConventionMapper() {
    super(COLUMNS, null);
  }

  public static ByteArrayResource fixedInflationSwapConventions() {
    return new ConventionalTradeConventionExporter<>(
            new FixedInflationSwapConventionMapper(), FixedInflationSwapConvention.class)
        .export();
  }
}
