package com.solum.xplain.core.curvemarket.node;

import static com.solum.xplain.core.error.Error.CALIBRATION_WARNING;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver;
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceTypeResolver;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.market.value.MarketDataKeyView;
import com.solum.xplain.core.market.value.MarketDataProviderTickerView;
import com.solum.xplain.core.market.value.MdkProviderBidAskType;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ValidMarketDataNodesFilter {

  private static final String INSTRUMENT_MISSING =
      "No curve configuration mapping found for instrument: %s";
  private static final String CONFIGURATION_PROVIDER_MISSING =
      "Curve configuration doesn't have mapping for required provider";
  private static final String MARKET_KEY_MISSING = "There is no mapping for key: %s";
  private static final String MARKET_KEY_PROVIDER_MISSING =
      "Market data key: %s does not have mapping for provider: %s";
  private static final String MARKET_KEY_BIDASK_MISSING =
      "Market data key: %s does not have mapping for required price type: %s";

  public static ValidNodesFilter ofValidMarketData(
      MarketDataSourceType sourceType,
      CurveConfigurationInstrumentResolver resolver,
      InstrumentPriceTypeResolver priceTypeResolver,
      List<MarketDataKeyView> keys,
      Consumer<List<ErrorItem>> errorLogger) {
    var keysMap =
        keys.stream()
            .collect(Collectors.toMap(MarketDataKeyView::getKey, Function.identity(), (a, b) -> a));

    return new ValidNodesFilter() {
      @Override
      public <T> List<T> filterNodes(List<NodeInstrumentWrapper<T>> wrappedNodes) {
        var eithers =
            Stream.ofNullable(wrappedNodes)
                .flatMap(Collection::stream)
                .map(v -> validateNode(v, sourceType, resolver, keysMap, priceTypeResolver))
                .toList();
        errorLogger.accept(ImmutableList.copyOf(Eithers.filterLeft(eithers).iterator()));

        return ImmutableList.copyOf(Eithers.filterRight(eithers).iterator());
      }
    };
  }

  private static <T> Either<ErrorItem, T> validateNode(
      NodeInstrumentWrapper<T> wrapper,
      MarketDataSourceType sourceType,
      CurveConfigurationInstrumentResolver resolver,
      Map<String, MarketDataKeyView> keys,
      InstrumentPriceTypeResolver priceTypeResolver) {
    var priceType = priceTypeResolver.resolvePriceType(wrapper.getDefinition());
    return validateCurveConfig(resolver, wrapper.getDefinition())
        .flatMap(p -> validateProvider(sourceType, p))
        .flatMap(p -> validateMapping(wrapper.getDefinition().getKey(), p, keys, priceType))
        .map(key -> wrapper.getNode());
  }

  private static Either<ErrorItem, MarketDataProviders> validateCurveConfig(
      CurveConfigurationInstrumentResolver resolver, InstrumentDefinition definition) {
    return resolver
        .resolveProvider(definition)
        .map(Either::<ErrorItem, MarketDataProviders>right)
        .orElse(
            Either.left(
                CALIBRATION_WARNING.entity(
                    String.format(INSTRUMENT_MISSING, definition.getKey()))));
  }

  private static Either<ErrorItem, String> validateProvider(
      MarketDataSourceType sourceType, MarketDataProviders provider) {
    return sourceType
        .resolveProvider(provider)
        .map(Either::<ErrorItem, String>right)
        .orElse(Either.left(CALIBRATION_WARNING.entity(CONFIGURATION_PROVIDER_MISSING)));
  }

  private static Either<ErrorItem, MarketDataKeyView> validateMapping(
      String key,
      String provider,
      Map<String, MarketDataKeyView> keys,
      InstrumentPriceType priceType) {
    var matchedKey = keys.get(key);
    return (matchedKey == null
            ? Either.<ErrorItem, MarketDataKeyView>left(
                CALIBRATION_WARNING.entity(String.format(MARKET_KEY_MISSING, key)))
            : Either.<ErrorItem, MarketDataKeyView>right(matchedKey))
        .flatMap(
            v ->
                Stream.ofNullable(v.getProviderTickers())
                    .flatMap(Collection::stream)
                    .map(MarketDataProviderTickerView::getCode)
                    .filter(c -> StringUtils.equals(provider, c))
                    .findFirst()
                    .map(c -> v)
                    .map(Either::<ErrorItem, MarketDataKeyView>right)
                    .orElse(
                        Either.left(
                            CALIBRATION_WARNING.entity(
                                String.format(MARKET_KEY_PROVIDER_MISSING, key, provider)))))
        .flatMap(
            v ->
                Stream.ofNullable(v.getProviderTickers())
                    .flatMap(Collection::stream)
                    .filter(pt -> StringUtils.equals(provider, pt.getCode()))
                    .flatMap(pt -> missingProviderBidAskTypes(priceType, pt.getBidAskType()))
                    .findFirst()
                    .map(
                        error ->
                            Either.<ErrorItem, MarketDataKeyView>left(
                                CALIBRATION_WARNING.entity(
                                    String.format(MARKET_KEY_BIDASK_MISSING, key, error))))
                    .orElse(Either.right(v)));
  }

  private static Stream<String> missingProviderBidAskTypes(
      InstrumentPriceType priceType, MdkProviderBidAskType providerBidAskType) {
    var error = priceType.missingRequiredValuesError(providerBidAskType.getSupportedValueBidAsks());
    return error == null ? Stream.empty() : Stream.of(error);
  }
}
