package com.solum.xplain.core.classifiers;

import static com.opengamma.strata.basics.currency.Currency.EUR;
import static com.opengamma.strata.basics.currency.Currency.GBP;
import static com.opengamma.strata.basics.currency.Currency.JPY;
import static com.opengamma.strata.basics.currency.Currency.USD;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.index.PriceIndex;
import com.opengamma.strata.product.credit.type.CdsConvention;
import com.opengamma.strata.product.credit.type.CdsConventions;
import com.opengamma.strata.product.swap.type.FixedInflationSwapConvention;
import com.opengamma.strata.product.swap.type.FixedInflationSwapConventions;
import com.opengamma.strata.product.swap.type.InflationRateSwapLegConvention;
import com.solum.xplain.core.classifiers.conventions.IborIndexConvention;
import com.solum.xplain.core.classifiers.conventions.OvernightIndexConvention;
import com.solum.xplain.core.classifiers.conventions.OvernightTermIndexConvention;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class PermissibleConventions {

  public static final Map<Currency, CdsConvention> CDS_CONVENTIONS =
      Map.of(
          EUR,
          CdsConventions.EUR_GB_STANDARD,
          USD,
          CdsConvention.of("USD-GB-STANDARD"),
          GBP,
          CdsConventions.GBP_STANDARD,
          JPY,
          CdsConventions.JPY_US_GB_STANDARD);

  public static final List<String> CREDIT_CURRENCIES =
      CDS_CONVENTIONS.keySet().stream().map(Currency::getCode).toList();

  public static final List<IborIndexConvention> IBOR_INDEX_CONVENTIONS =
      Collections.unmodifiableList(iborIndexConventions());

  public static final List<OvernightIndexConvention> OVERNIGHT_INDEX_CONVENTIONS =
      Collections.unmodifiableList(overnightIndexConventions());

  public static final List<OvernightTermIndexConvention> OVERNIGHT_TERM_INDEX_CONVENTIONS =
      Collections.unmodifiableList(overnightTermIndexConventions());

  public static final List<FixedInflationSwapConvention> FIXED_INFLATION_CONVENTIONS =
      Collections.unmodifiableList(fixedInflationConventions());

  public static final Map<PriceIndex, InflationRateSwapLegConvention>
      FIXED_INFLATION_CONVENTION_BY_INDEX =
          Collections.unmodifiableMap(inflationRateSwapLegConventionByIndex());

  private static List<IborIndexConvention> iborIndexConventions() {
    return Constants.IBOR_INDICES.stream().map(IborIndexConvention::newOf).toList();
  }

  private static List<OvernightIndexConvention> overnightIndexConventions() {
    return Constants.OVERNIGHT_INDICES.stream().map(OvernightIndexConvention::newOf).toList();
  }

  private static List<OvernightTermIndexConvention> overnightTermIndexConventions() {
    return Constants.OVERNIGHT_TERM_INDICES.stream()
        .map(OvernightTermIndexConvention::newOf)
        .toList();
  }

  private static List<FixedInflationSwapConvention> fixedInflationConventions() {
    return FixedInflationSwapConvention.extendedEnum().lookupAllNormalized().values().stream()
        .filter(c -> !Objects.equals(FixedInflationSwapConventions.JPY_FIXED_ZC_JP_CPI, c))
        .toList();
  }

  private static Map<PriceIndex, InflationRateSwapLegConvention>
      inflationRateSwapLegConventionByIndex() {
    return FIXED_INFLATION_CONVENTIONS.stream()
        .map(FixedInflationSwapConvention::getFloatingLeg)
        .collect(
            Collectors.toMap(
                InflationRateSwapLegConvention::getIndex,
                Function.identity(),
                (convention1, convention2) -> convention1));
  }
}
