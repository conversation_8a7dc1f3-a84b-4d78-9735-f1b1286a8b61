package com.solum.xplain.core.classifiers.xm;

import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.lang.NonNull;

@AllArgsConstructor
public enum CoreThresholdLevel implements ThresholdLevel {
  LEVEL_NULL("NULL"),
  LEVEL_1("1"),
  LEVEL_2("2"),
  LEVEL_3("3");

  private final String level;

  public static final List<String> NAMES =
      List.of(LEVEL_NULL.name(), LEVEL_1.name(), LEVEL_2.name(), LEVEL_3.name());

  @Override
  public String label() {
    return level;
  }

  public static CoreThresholdLevel from(@NonNull Integer level) {
    for (CoreThresholdLevel thresholdLevel : CoreThresholdLevel.values()) {
      if (thresholdLevel.level.equals(level.toString())) {
        return thresholdLevel;
      }
    }
    throw new IllegalArgumentException("Invalid threshold level: " + level);
  }
}
