package com.solum.xplain.core.classifiers;

import static com.opengamma.strata.basics.currency.Currency.EUR;
import static com.opengamma.strata.basics.currency.Currency.JPY;
import static com.opengamma.strata.basics.currency.Currency.USD;
import static com.solum.xplain.extensions.enums.CreditDocClause.CR;
import static com.solum.xplain.extensions.enums.CreditDocClause.CR14;
import static com.solum.xplain.extensions.enums.CreditDocClause.MM14;
import static com.solum.xplain.extensions.enums.CreditDocClause.MR14;
import static com.solum.xplain.extensions.enums.CreditDocClause.XR;
import static com.solum.xplain.extensions.enums.CreditDocClause.XR14;
import static com.solum.xplain.extensions.enums.CreditSector.DIVERSIFIED;
import static com.solum.xplain.extensions.enums.CreditSector.FINANCIALS;
import static com.solum.xplain.extensions.enums.CreditSector.GOVERNMENT;
import static com.solum.xplain.extensions.enums.CreditSector.MUNICIPALITIES;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.extensions.enums.CreditDocClause;
import com.solum.xplain.extensions.enums.CreditSector;
import java.util.Optional;
import lombok.Getter;
import org.springframework.lang.Nullable;

@Getter
public enum CdsIndex {
  CDX_EM("CDX EM", USD, DIVERSIFIED, CR14),
  CDX_NA_HY("CDX NA HY", USD, DIVERSIFIED, XR14),
  CDX_NA_IG("CDX NA IG", USD, DIVERSIFIED, XR14),
  CDX_LATAM("CDX LATAM", USD, DIVERSIFIED, CR14),
  ITRAXX_ASIA_EX_JAPAN_IG("ITRAXX ASIA EX JAPAN IG", USD, DIVERSIFIED, CR14),
  ITRAXX_AUSTRALIA("ITRAXX AUSTRALIA", USD, DIVERSIFIED, MR14),
  ITRAXX_EUR("ITRAXX EUR", EUR, DIVERSIFIED, MM14),
  ITRAXX_EUR_HIVOL("ITRAXX EUR HIVOL", USD, DIVERSIFIED, MM14),
  ITRAXX_EUR_SNR_FINANCIALS("ITRAXX EUR SNR FINANCIALS", EUR, FINANCIALS, MM14),
  ITRAXX_EUR_SUB_FINANCIALS("ITRAXX EUR SUB FINANCIALS", EUR, FINANCIALS, MM14),
  ITRAXX_EUR_XOVER("ITRAXX EUR XOVER", EUR, DIVERSIFIED, MM14),
  ITRAXX_JAPAN("ITRAXX JAPAN", JPY, DIVERSIFIED, CR14),
  ITRAXX_SOVX_WESTN_EUROPE("ITRAXX SOVX WESTN EUROPE", USD, GOVERNMENT, CR),
  ITRAXX_SOVX_CEEMEA_EXEU("ITRAXX SOVX CEEMEA exEU", USD, GOVERNMENT, CR14),
  ITRAXX_SOVX_CEEMEA("ITRAXX SOVX CEEMEA", USD, GOVERNMENT, CR14),
  MCDX_NA("MCDX NA", USD, MUNICIPALITIES, XR),

  CMBX_NA_AAA("CMBX NA AAA", USD, CreditSector.UNDEFINED, CreditDocClause.UNDEFINED),
  CMBX_NA_AM("CMBX NA AM", USD, CreditSector.UNDEFINED, CreditDocClause.UNDEFINED),
  CMBX_NA_AJ("CMBX NA AJ", USD, CreditSector.UNDEFINED, CreditDocClause.UNDEFINED),
  CMBX_NA_AS("CMBX NA AS", USD, CreditSector.UNDEFINED, CreditDocClause.UNDEFINED),
  CMBX_NA_AA("CMBX NA AA", USD, CreditSector.UNDEFINED, CreditDocClause.UNDEFINED),
  CMBX_NA_A("CMBX NA A", USD, CreditSector.UNDEFINED, CreditDocClause.UNDEFINED),
  CMBX_NA_BBB("CMBX NA BBB", USD, CreditSector.UNDEFINED, CreditDocClause.UNDEFINED),
  CMBX_NA_BBB_MINUS("CMBX NA BBB-", USD, CreditSector.UNDEFINED, CreditDocClause.UNDEFINED),
  CMBX_NA_BB("CMBX NA BB", USD, CreditSector.UNDEFINED, CreditDocClause.UNDEFINED),
  ABX_HE("ABX HE", USD, CreditSector.UNDEFINED, CreditDocClause.UNDEFINED),
  ABX_HE_PENAAA("ABX HE PENAAA", USD, CreditSector.UNDEFINED, CreditDocClause.UNDEFINED),
  ABX_HE_AAA("ABX HE AAA", USD, CreditSector.UNDEFINED, CreditDocClause.UNDEFINED),
  ABX_HE_AA("ABX HE AA", USD, CreditSector.UNDEFINED, CreditDocClause.UNDEFINED),
  ABX_HE_A("ABX HE A", USD, CreditSector.UNDEFINED, CreditDocClause.UNDEFINED),
  ABX_HE_BBB("ABX HE BBB", USD, CreditSector.UNDEFINED, CreditDocClause.UNDEFINED),
  ABX_HE_BBB_MINUS("ABX HE BBB-", USD, CreditSector.UNDEFINED, CreditDocClause.UNDEFINED);

  private final String label;
  private final Currency currency;
  private final CreditSector sector;
  private final CreditDocClause docClause;

  CdsIndex(String label, Currency currency, CreditSector sector, CreditDocClause docClause) {
    this.label = label;
    this.currency = currency;
    this.sector = sector;
    this.docClause = docClause;
  }

  @Nullable
  public static String labelValueOf(@Nullable String cdsIndex) {
    return Optional.ofNullable(cdsIndex)
        .map(CdsIndex::valueOf)
        .map(CdsIndex::getLabel)
        .orElse(null);
  }
}
