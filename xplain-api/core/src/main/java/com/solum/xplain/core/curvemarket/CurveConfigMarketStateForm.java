package com.solum.xplain.core.curvemarket;

import static com.solum.xplain.core.curvemarket.CurveConfigMarketStateKey.configMarketKey;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;
import static org.mapstruct.factory.Mappers.getMapper;

import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirementsForm;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirementsMapper;
import java.time.LocalDate;
import java.util.Optional;
import lombok.Data;
import org.springdoc.core.annotations.ParameterObject;

@Data
@ParameterObject
public class CurveConfigMarketStateForm {

  private final String marketDataGroupId;
  private final String configurationId;
  private final MarketDataSourceType marketDataSource;
  private final LocalDate stateDate;
  private final LocalDate curveDate;

  private final InstrumentPriceRequirementsForm priceRequirements;

  public CurveConfigMarketStateForm(
      String marketDataGroupId,
      String configurationId,
      MarketDataSourceType marketDataSource,
      LocalDate stateDate,
      LocalDate curveDate,
      InstrumentPriceRequirementsForm priceRequirements) {
    this.marketDataGroupId = marketDataGroupId;
    this.configurationId = configurationId;
    this.marketDataSource = marketDataSource;
    this.stateDate = stateDate;
    this.curveDate = curveDate;
    this.priceRequirements =
        priceRequirements != null ? priceRequirements : new InstrumentPriceRequirementsForm();
  }

  public Optional<CurveConfigMarketStateKey> toKey() {
    if (isNotEmpty(marketDataGroupId)
        && isNotEmpty(configurationId)
        && marketDataSource != null
        && stateDate != null
        && curveDate != null) {
      return Optional.of(
          configMarketKey(
              marketDataGroupId,
              configurationId,
              marketDataSource,
              bitemporalDate(),
              curveDate,
              priceRequirements()));
    }
    return Optional.empty();
  }

  public BitemporalDate bitemporalDate() {
    return BitemporalDate.newOf(stateDate);
  }

  public InstrumentPriceRequirements priceRequirements() {
    return getMapper(InstrumentPriceRequirementsMapper.class).fromForm(priceRequirements);
  }
}
