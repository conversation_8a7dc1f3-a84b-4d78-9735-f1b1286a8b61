package com.solum.xplain.core.classifiers;

import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class ClassifiersControllerService {

  public static final String DOC_CLAUSE_CLASSIFIER_NAME = "creditDocClauses";
  public static final String SECTOR_CLASSIFIER_NAME = "creditSectors";
  public static final String CREDIT_INDEX_CLASSIFIER_NAME = "cdsIndex";
  public static final String CREDIT_INDEX_TRANCHE_CLASSIFIER_NAME = "creditTrancheIndices";
  public static final String IR_INSTRUMENTS_CLASSIFIER_NAME = "irInstruments";
  public static final String CORE_ASSET_CLASSES = "coreAssetClasses";
  public static final String CURRENCY_CLASSIFIER = "currency";
  public static final String CURRENCY_PAIR_CLASSIFIER = "currencyPair";
  public static final String CURVE_NODE_CLASH_ACTION_CLASSIFIER = "curveNodeClashAction";
  public static final String STRIPPING_TYPE_CLASSIFIER = "strippingType";
  public static final String CREDIT_FREQUENCY_CLASSIFIER = "creditFrequency";
  public static final String MIN_NODE_GAP_CLASSIFIER = "minNodeGap";
  public static final String CAPLET_VALUATION_MODEL_CLASSIFIER = "capletValuationModel";
  public static final String VOLATILITY_TYPE_CLASSIFIER = "volatilityType";
  public static final String SWAPTION_SETTLEMENT_TYPE_CLASSIFIER = "swaptionSettlementType";
  public static final String DEFAULT_CURVE_VALUE_TYPE_CLASSIFIER =
      "defaultCurveYInterpolationMethod";
  public static final String INFLATION_CURVE_VALUE_TYPE_CLASSIFIER =
      "inflationCurveYInterpolationMethod";
  public static final String CURVE_EXTRAPOLATOR_CLASSIFIER = "curveExtrapolator";
  public static final String CURVE_DISCOUNT_FACTOR_INTERP_EXTRAPOLATOR_CLASSIFIER =
      "curveDiscountFactorInterpExtrapolators";
  public static final String SURFACE_EXTRAPOLATOR_CLASSIFIER = "surfaceExtrapolator";
  public static final String CURVE_INTERPOLATOR_CLASSIFIER = "curveInterpolator";
  public static final String DISCOUNT_FACTOR_CURVE_INTERPOLATOR_CLASSIFIER =
      "discountFactorCurveInterpolator";
  public static final String CREDIT_QUOTE_CONVENTION_CLASSIFIER = "cdsQuoteConvention";
  public static final String CREDIT_SENIORITY_CLASSIFIER = "creditCurveSeniority";
  public static final String BUSINESS_DAY_ADJUSTMENT_TYPE = "businessDayAdjustmentType";
  public static final String INFLATION_BUSINESS_DAY_ADJUSTMENT_TYPE =
      "inflationBusinessDayAdjustmentType";
  public static final String ROLL_CONVENTION_CLASSIFIER = "rollConvention";
  public static final String SUPPORTED_CALENDARS_CLASSIFIER = "supportedCalendars";
  public static final String OVERNIGHT_ACCRUAL_METHOD_CLASSIFIER = "overnightAccrualMethod";
  public static final String OVERNIGHT_TERM_ACCRUAL_METHOD_CLASSIFIER =
      "overnightTermAccrualMethod";
  public static final String FIXED_ACCRUAL_METHOD_CLASSIFIER = "fixedAccrualMethod";
  public static final String STUB_CONVENTION_CLASSIFIER = "stubConvention";

  private final ClassifiersAggregator classifiersAggregator;

  public ClassifiersControllerService(ClassifiersAggregator classifiersAggregator) {
    this.classifiersAggregator = classifiersAggregator;
  }

  public List<Classifier> getClassifiers() {
    return classifiersAggregator.classifiers();
  }
}
