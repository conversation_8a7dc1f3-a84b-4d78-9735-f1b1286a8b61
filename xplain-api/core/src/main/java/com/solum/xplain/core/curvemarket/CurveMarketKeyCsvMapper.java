package com.solum.xplain.core.curvemarket;

import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.market.csv.MarketDataKeyCsvMapper;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CurveMarketKeyCsvMapper {

  private static final List<String> CSV_HEADER =
      List.of(
          MarketDataKeyCsvMapper.NAME_FIELD,
          MarketDataKeyCsvMapper.KEY_FIELD,
          MarketDataKeyCsvMapper.ASSET_CLASS,
          MarketDataKeyCsvMapper.INSTRUMENT_TYPE);

  public static CsvRow toCsvRow(InstrumentDefinition key) {
    return new CsvRow(
        List.of(
            new CsvField(MarketDataKeyCsvMapper.NAME_FIELD, key.getMdkName()),
            new CsvField(MarketDataKeyCsvMapper.KEY_FIELD, key.getKey()),
            new CsvField(MarketDataKeyCsvMapper.ASSET_CLASS, key.getAssetClass().getGroup().name()),
            new CsvField(MarketDataKeyCsvMapper.INSTRUMENT_TYPE, key.getInstrument().getLabel())));
  }

  public static CsvOutputFile csvOutputFile(List<CsvRow> rows) {
    return new CsvOutputFile(CSV_HEADER, rows);
  }
}
