package com.solum.xplain.core.classifiers.sladeadlines;

import com.solum.xplain.core.common.LabeledEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum SlaDeadline implements LabeledEnum {
  OTHER("OTHER"),
  LDN_1200("LDN 1200"),
  LDN_1500("LDN 1500"),
  LDN_1600("LDN 1600"),
  LDN_1615("LDN 1615"),
  LDN_1700("LDN 1700"),
  LDN_COB("LDN COB"),
  LDN_1000_T_PLUS("LDN 1000 T+1"),
  NYC_1200("NYC 1200"),
  NYC_1500("NYC 1500"),
  NYC_1600("NYC 1600"),
  NYC_1615("NYC 1615"),
  NYC_1700("NYC 1700"),
  NYC_COB("NYC COB"),
  NYC_1000_T_PLUS("NYC 1000 T+1");

  private final String label;
}
