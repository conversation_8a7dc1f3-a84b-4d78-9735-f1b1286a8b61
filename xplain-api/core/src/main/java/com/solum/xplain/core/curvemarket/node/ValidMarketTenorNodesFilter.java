package com.solum.xplain.core.curvemarket.node;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.date.MarketTenor;
import com.opengamma.strata.market.curve.CurveNodeDateOrder;
import com.opengamma.strata.product.fx.type.FxSwapConvention;
import com.solum.xplain.core.classifiers.CurveNodeTypes;
import com.solum.xplain.core.curvegroup.conventions.ClearingHouse;
import com.solum.xplain.core.curvegroup.curve.entity.CurveNode;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Stream;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ValidMarketTenorNodesFilter {
  private static final String INVALID_TN_END_DATE =
      "Market tenor node with key %s was dropped since its end date exceeds the spot date";

  public static ValidNodesFilter ofValidMarketTenor(
      LocalDate valuationDate,
      ReferenceData referenceData,
      CurveNodeDateOrder nodeDateOrder,
      Consumer<List<ErrorItem>> warningsConsumer) {

    return new ValidNodesFilter() {
      @Override
      public <T> List<T> filterNodes(List<NodeInstrumentWrapper<T>> wrappedNodes) {
        var eithers =
            Stream.ofNullable(wrappedNodes)
                .flatMap(Collection::stream)
                .map(v -> validateNode(v, nodeDateOrder, valuationDate, referenceData))
                .toList();
        warningsConsumer.accept(ImmutableList.copyOf(Eithers.filterLeft(eithers).iterator()));

        return ImmutableList.copyOf(Eithers.filterRight(eithers).iterator());
      }
    };
  }

  private static <T> Either<ErrorItem, T> validateNode(
      NodeInstrumentWrapper<T> wrapper,
      CurveNodeDateOrder nodeDateOrder,
      LocalDate valuationDate,
      ReferenceData referenceData) {
    var node = wrapper.getNode();
    if (node instanceof CurveNode curveNode) {
      if (isFxTNNode(curveNode)
          && invalidTNNode(curveNode, nodeDateOrder, valuationDate, referenceData)) {
        return Either.left(
            Error.CALIBRATION_WARNING.entity(
                String.format(INVALID_TN_END_DATE, wrapper.getDefinition().getKey())));
      }
    }
    return Either.right(node);
  }

  private static boolean isFxTNNode(CurveNode curveNode) {
    return CurveNodeTypes.FX_SWAP_NODE.equalsIgnoreCase(curveNode.getType())
        && curveNode.getPeriod().equals(MarketTenor.TN.getCode());
  }

  private static boolean invalidTNNode(
      CurveNode node,
      CurveNodeDateOrder nodeDateOrder,
      LocalDate valuationDate,
      ReferenceData referenceData) {
    var convention = FxSwapConvention.of(node.getConvention());
    // FX nodes can't be offshore. Date calculation does not take into account clearing house
    var endDate = node.date(false, ClearingHouse.NONE, valuationDate, nodeDateOrder, referenceData);
    var spotDate = convention.calculateSpotDateFromTradeDate(valuationDate, referenceData);
    return endDate.isAfter(spotDate);
  }
}
