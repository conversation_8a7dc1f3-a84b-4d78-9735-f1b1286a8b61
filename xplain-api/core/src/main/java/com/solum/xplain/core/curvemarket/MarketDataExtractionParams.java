package com.solum.xplain.core.curvemarket;

import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceTypeResolver.priceTypeResolver;

import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceTypeResolver;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.mdvalue.MarketDataValueService;
import com.solum.xplain.core.mdvalue.value.ResolvedMarketData;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.lang.NonNull;

@Data
@AllArgsConstructor(access = AccessLevel.PROTECTED)
public abstract class MarketDataExtractionParams {
  @NonNull private final CurveConfigMarketStateKey stateKey;
  @NonNull private final InstrumentPriceTypeResolver priceTypeResolver;

  public abstract Either<ErrorItem, Map<String, ResolvedMarketData>> resolvedValuesByKey(
      MarketDataValueService marketDataValueService);

  public MarketDataSourceType getMarketDataSource() {
    return stateKey.getMarketDataSource();
  }

  public BitemporalDate getStateDate() {
    return stateKey.getStateDate();
  }

  public String getMarketDataGroupId() {
    return stateKey.getMarketDataGroupId();
  }

  public LocalDate getCurveDate() {
    return stateKey.getCurveDate();
  }

  public String getConfigurationId() {
    return stateKey.getConfigurationId();
  }

  public InstrumentPriceTypeResolver getPriceTypeResolver() {
    return priceTypeResolver;
  }

  protected static InstrumentPriceTypeResolver mdParamsWithCurvesResolver(
      InstrumentPriceRequirements priceRequirements, List<String> discountCurves) {
    return priceTypeResolver(priceRequirements, discountCurves);
  }

  protected static InstrumentPriceTypeResolver mdParamsWithoutCurvesResolver(
      InstrumentPriceRequirements priceRequirements) {
    return priceTypeResolver(priceRequirements, List.of());
  }
}
