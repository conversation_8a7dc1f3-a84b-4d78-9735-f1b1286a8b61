package com.solum.xplain.core.curvemarket.pricetype;

import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.ASK;
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.BID;
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.MID;
import static java.math.MathContext.DECIMAL64;
import static java.util.Optional.ofNullable;

import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import java.math.BigDecimal;
import java.util.Optional;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.Nullable;

@AllArgsConstructor
@Getter
public enum InstrumentPriceType {
  BID_PRICE("Bid"),
  MID_PRICE("Mid"),
  ASK_PRICE("Ask");

  private final String label;

  public @Nullable String missingRequiredValuesError(Set<ValueBidAskType> types) {
    return switch (this) {
      case BID_PRICE -> types.contains(BID) ? null : "Bid";
      case MID_PRICE ->
          (types.contains(BID) && types.contains(ASK)) || types.contains(MID)
              ? null
              : "Mid or Bid/Ask";
      case ASK_PRICE -> types.contains(ASK) ? null : "Ask";
    };
  }

  public Optional<BigDecimal> calculateRequiredPrice(
      @Nullable BigDecimal askValue, @Nullable BigDecimal midValue, @Nullable BigDecimal bidValue) {
    return switch (this) {
      case BID_PRICE -> ofNullable(bidValue);
      case MID_PRICE -> ofNullable(midValue).or(() -> averageOrEmpty(askValue, bidValue));
      case ASK_PRICE -> ofNullable(askValue);
    };
  }

  private static Optional<BigDecimal> averageOrEmpty(BigDecimal askValue, BigDecimal bidValue) {
    return ofNullable(bidValue).flatMap(b -> ofNullable(askValue).map(a -> ave(b, a)));
  }

  private static BigDecimal ave(BigDecimal b, BigDecimal a) {
    return b.add(a).divide(BigDecimal.valueOf(2), DECIMAL64);
  }
}
