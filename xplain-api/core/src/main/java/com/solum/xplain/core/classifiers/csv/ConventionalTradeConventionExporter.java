package com.solum.xplain.core.classifiers.csv;

import com.opengamma.strata.collect.named.Named;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConvention;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.Collection;
import java.util.Comparator;
import lombok.AllArgsConstructor;
import org.springframework.core.io.ByteArrayResource;

@AllArgsConstructor
public class ConventionalTradeConventionExporter<T extends Named> {
  private final CsvMapper<T> mapper;
  private final Class<T> conventionClass;

  public ByteArrayResource export() {
    var rowsStream =
        ConventionalCurveConfigurations.ALL_CONVENTIONAL_CURVES.stream()
            .map(ConventionalCurveConvention::getNodeConventions)
            .flatMap(Collection::stream)
            .filter(conventionClass::isInstance)
            .map(conventionClass::cast)
            .distinct()
            .sorted(Comparator.comparing(Named::getName))
            .map(mapper::toCsvRow)
            .map(Either::<ErrorItem, CsvRow>right);
    return new CsvOutputFile(mapper.header(), rowsStream).writeToByteArray();
  }
}
