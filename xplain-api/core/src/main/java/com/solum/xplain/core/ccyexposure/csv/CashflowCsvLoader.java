package com.solum.xplain.core.ccyexposure.csv;

import static com.solum.xplain.core.ccyexposure.csv.CcyExposureCsvFields.CASHFLOW_AMOUNT_FIELD;
import static com.solum.xplain.core.ccyexposure.csv.CcyExposureCsvFields.CASHFLOW_DATE_FIELD;
import static com.solum.xplain.core.ccyexposure.csv.CcyExposureCsvFields.CCY_EXPOSURE_NAME_FIELD;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseDate;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseDouble;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.readCsv;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateEmptyContent;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateHeaders;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.error.Error.IMPORT_ERROR;
import static com.solum.xplain.core.error.Error.IMPORT_WARNING;
import static com.solum.xplain.core.error.Error.PARSING_ERROR;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;
import static java.lang.String.format;
import static org.springframework.beans.factory.config.ConfigurableBeanFactory.SCOPE_PROTOTYPE;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.collect.io.CsvFile;
import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.ccyexposure.entity.CcyExposure;
import com.solum.xplain.core.ccyexposure.value.CashflowForm;
import com.solum.xplain.core.common.csv.DuplicateAction;
import com.solum.xplain.core.common.csv.ItemsCsvResult;
import com.solum.xplain.core.common.csv.ItemsGroupCsvResult;
import com.solum.xplain.core.common.csv.NamedList;
import com.solum.xplain.core.common.csv.TargettedItemParsedRow;
import com.solum.xplain.core.common.diff.AuditableDiffable;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.ErrorItem.ListOfErrors;
import com.solum.xplain.core.error.WarningItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import org.springframework.context.annotation.Scope;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

/**
 * Loader for cashflow CSV files.
 *
 * <p>This is a prototype bean so that we can cache data in ccyExposureIdsByName on each invocation.
 */
@Scope(SCOPE_PROTOTYPE)
@Component
public class CashflowCsvLoader {

  private Map<String, String> ccyExposureIdsByName;

  public ItemsGroupCsvResult<CashflowForm> parse(
      byte[] csvContent, List<CcyExposure> ccyExposure, DuplicateAction action) {
    ccyExposureIdsByName =
        ccyExposure.stream()
            .collect(Collectors.toMap(CcyExposure::getName, AuditableDiffable::getId));

    return parse(
        csvContent,
        validCashflowFn(ccyExposure, action),
        rowNameFieldIsKeyOf(ccyExposureIdsByName));
  }

  private Predicate<CsvRow> rowNameFieldIsKeyOf(Map<String, String> ccyExposureIdsByName) {
    return row ->
        row.findValue(CCY_EXPOSURE_NAME_FIELD)
            .filter(ccyExposureIdsByName::containsKey)
            .isPresent();
  }

  protected List<String> getFileHeaders() {
    return List.of(CCY_EXPOSURE_NAME_FIELD, CASHFLOW_DATE_FIELD, CASHFLOW_AMOUNT_FIELD);
  }

  protected String getCcyExposureNameField() {
    return CCY_EXPOSURE_NAME_FIELD;
  }

  protected String getCcyExposureName(CcyExposure ccyExposure) {
    return ccyExposure.getName();
  }

  protected Either<ErrorItem, CashflowForm> parse(@NonNull CsvRow row) {
    try {
      return right(
          new CashflowForm(
              parseCsvExposureId(row),
              parseDate(row.getValue(CASHFLOW_DATE_FIELD)),
              parseDouble(row, CASHFLOW_AMOUNT_FIELD)));
    } catch (RuntimeException e) {
      return left(
          new ErrorItem(
              PARSING_ERROR,
              format("Error parsing line %d: %s", row.lineNumber(), e.getMessage())));
    }
  }

  private String parseCsvExposureId(CsvRow row) {
    return row.getValue(
        CCY_EXPOSURE_NAME_FIELD,
        v -> {
          validateValue(v, ccyExposureIdsByName.keySet());
          return ccyExposureIdsByName.get(v);
        });
  }

  protected Either<List<ErrorItem>, NamedList<CashflowForm>> validateCashflows(
      CcyExposure ccyExposure, NamedList<CashflowForm> cashflowsGroup, DuplicateAction action) {
    Objects.requireNonNull(ccyExposure);
    Objects.requireNonNull(action);
    return right(cashflowsGroup);
  }

  private ItemsGroupCsvResult<CashflowForm> parse(
      byte[] csvContent,
      Function<NamedList<CashflowForm>, Either<List<ErrorItem>, NamedList<CashflowForm>>>
          validcashflowsFn,
      Predicate<CsvRow> isRelevantLine) {
    try {
      CsvFile csvFile = readCsv(csvContent);

      validateHeaders(
          csvFile,
          ImmutableList.<String>builder()
              .add(getCcyExposureNameField())
              .addAll(getFileHeaders())
              .build());
      validateEmptyContent(csvFile);

      Set<Entry<String, LocalDate>> existingExposureNames = new HashSet<>();
      List<ErrorItem> errors = new ArrayList<>();
      List<WarningItem> warnings = new ArrayList<>();
      List<TargettedItemParsedRow<CashflowForm>> parsedRows = new ArrayList<>();
      for (CsvRow csvRow : csvFile.rows()) {
        if (isRelevantLine.test(csvRow)) {
          Either<ErrorItem, TargettedItemParsedRow<CashflowForm>> parsed =
              parseLine(csvRow)
                  .map(r -> r.validateNode(checkUniqueCashflow(csvRow, existingExposureNames)));
          parsed
              .right()
              .forEach(
                  cashflow -> {
                    parsedRows.add(cashflow);
                    cashflow
                        .getItemEither()
                        .forEach(
                            n ->
                                existingExposureNames.add(
                                    Map.entry(
                                        cashflow.getTargetName(),
                                        parseDate(csvRow.getValue(CASHFLOW_DATE_FIELD)))));
                  });
          parsed.left().forEach(errors::add);
        } else {
          warnings.add(
              WarningItem.of(
                  IMPORT_WARNING, format("Skipping line %d: %s", csvRow.lineNumber(), csvRow)));
        }
      }

      return groupResults(parsedRows)
          .withErrors(errors)
          .withWarnings(warnings)
          .validate(validcashflowsFn);

    } catch (Exception e) {
      return groupResults(List.of())
          .withErrors(List.of(new ErrorItem(PARSING_ERROR, e.getMessage())));
    }
  }

  private BiFunction<
          TargettedItemParsedRow<CashflowForm>, CashflowForm, Either<ErrorItem, CashflowForm>>
      checkUniqueCashflow(CsvRow row, Set<Entry<String, LocalDate>> existingExposureNames) {
    return (parsedRow, t) -> {
      LocalDate date = parseDate(row.getValue(CASHFLOW_DATE_FIELD));
      Entry<String, LocalDate> entry = Map.entry(parsedRow.getTargetName(), date);
      if (existingExposureNames.contains(entry)) {
        return left(
            new ErrorItem(
                PARSING_ERROR,
                format(
                    "Error parsing line %d: Duplicate found: %s %s",
                    row.lineNumber(), parsedRow.getTargetName(), date)));
      } else {
        existingExposureNames.add(entry);
        return right(t);
      }
    };
  }

  private Either<ErrorItem, TargettedItemParsedRow<CashflowForm>> parseLine(@NonNull CsvRow row) {
    return row.findValue(getCcyExposureNameField())
        .map(n -> TargettedItemParsedRow.fromEither(n, parse(row)))
        .map(Either::<ErrorItem, TargettedItemParsedRow<CashflowForm>>right)
        .orElse(
            left(
                new ErrorItem(
                    PARSING_ERROR,
                    format(
                        "Error parsing line %d: %s field missing or empty",
                        row.lineNumber(), getCcyExposureNameField()))));
  }

  private ItemsGroupCsvResult<CashflowForm> groupResults(
      List<TargettedItemParsedRow<CashflowForm>> rows) {
    return ItemsGroupCsvResult.fromList(
        rows.stream()
            .collect(
                Collectors.collectingAndThen(
                    Collectors.groupingBy(TargettedItemParsedRow::getTargetName), this::fromMap)));
  }

  private List<ItemsCsvResult<CashflowForm>> fromMap(
      Map<String, List<TargettedItemParsedRow<CashflowForm>>> map) {
    return map.values().stream()
        .flatMap(
            e ->
                e.stream()
                    .map(ItemsCsvResult::fromParsedRows)
                    .reduce(ItemsCsvResult::joining)
                    .stream())
        .toList();
  }

  private Function<NamedList<CashflowForm>, Either<List<ErrorItem>, NamedList<CashflowForm>>>
      validCashflowFn(List<CcyExposure> ccyExposures, DuplicateAction duplicateAction) {
    return cashflows ->
        validateExposureExists(ccyExposures, cashflows)
            .leftMap(ListOfErrors::from)
            .flatMap(c -> validateCashflows(c, cashflows, duplicateAction));
  }

  private <F> Either<ErrorItem, CcyExposure> validateExposureExists(
      List<CcyExposure> ccyExposures, NamedList<F> cashflowsGroup) {
    return exposureForName(ccyExposures, cashflowsGroup.getName());
  }

  private Either<ErrorItem, CcyExposure> exposureForName(
      List<CcyExposure> ccyExposures, String name) {
    return ccyExposures.stream()
        .filter(c -> Objects.equals(getCcyExposureName(c), name))
        .findFirst()
        .map(Either::<ErrorItem, CcyExposure>right)
        .orElseGet(() -> left(exposureForCashflowNotFound(name)));
  }

  private ErrorItem exposureForCashflowNotFound(String exposureName) {
    return new ErrorItem(
        IMPORT_ERROR, format("Exposure with name: %s was not found", exposureName));
  }
}
