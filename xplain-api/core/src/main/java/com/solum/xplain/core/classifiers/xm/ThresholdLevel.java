package com.solum.xplain.core.classifiers.xm;

import java.io.Serializable;

/**
 * Interface to allow greater flexibility in future, but also this prevents Spring Data from
 * misusing the enum converter to convert all strings to ThresholdLevel if it were an enum.
 */
@SuppressWarnings("InterfaceWithOnlyOneDirectInheritor")
public interface ThresholdLevel extends Serializable {
  String name();

  String label();
}
