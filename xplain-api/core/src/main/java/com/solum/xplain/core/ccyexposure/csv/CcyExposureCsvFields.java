package com.solum.xplain.core.ccyexposure.csv;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CcyExposureCsvFields {
  static final String CCY_EXPOSURE_NAME_FIELD = "Ccy Exposure Name";
  static final String CCY_EXPOSURE_CURRENCY_FIELD = "Currency";
  static final String CCY_EXPOSURE_DESCRIPTION_FIELD = "Description";

  static final String CASHFLOW_DATE_FIELD = "Date";
  static final String CASHFLOW_AMOUNT_FIELD = "Amount";
}
