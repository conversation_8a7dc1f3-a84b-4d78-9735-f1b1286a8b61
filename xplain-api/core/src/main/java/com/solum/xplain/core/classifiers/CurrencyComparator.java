package com.solum.xplain.core.classifiers;

import static com.solum.xplain.core.classifiers.Constants.EXPLICIT_CURRENCIES;

import com.opengamma.strata.basics.currency.Currency;
import java.util.Comparator;

public class CurrencyComparator implements Comparator<Currency> {

  public static final CurrencyComparator INSTANCE = new CurrencyComparator();

  @Override
  public int compare(Currency o1, Currency o2) {
    int o1Idx = EXPLICIT_CURRENCIES.indexOf(o1);
    int o2Idx = EXPLICIT_CURRENCIES.indexOf(o2);
    if (o1Idx < 0 && o2Idx < 0) {
      return o1.getCode().compareTo(o2.getCode());
    } else if (o1Idx < 0) {
      return 1;
    } else if (o2Idx < 0) {
      return -1;
    } else {
      return o1Idx - o2Idx;
    }
  }
}
