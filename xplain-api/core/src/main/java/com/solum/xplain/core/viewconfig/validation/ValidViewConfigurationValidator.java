package com.solum.xplain.core.viewconfig.validation;

import static java.util.function.Predicate.not;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.viewconfig.ViewConfigurationRepository;
import com.solum.xplain.core.viewconfig.entity.ViewConfiguration;
import com.solum.xplain.core.viewconfig.provider.PaletteService;
import com.solum.xplain.core.viewconfig.value.ColumnDefinitionForm;
import com.solum.xplain.core.viewconfig.value.ColumnDefinitionGroupForm;
import com.solum.xplain.core.viewconfig.value.FieldDefinitionView;
import com.solum.xplain.core.viewconfig.value.PaletteView;
import com.solum.xplain.core.viewconfig.value.View;
import com.solum.xplain.core.viewconfig.value.ViewConfigurationCreateForm;
import com.solum.xplain.core.viewconfig.value.ViewConfigurationUpdateForm;
import io.atlassian.fugue.Checked;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class ValidViewConfigurationValidator
    implements ConstraintValidator<ValidViewConfiguration, ViewConfigurationUpdateForm> {

  private final ViewConfigurationRepository repository;
  private final PaletteService paletteService;
  private final RequestPathVariablesSupport pathVariablesSupport;

  @Override
  public boolean isValid(ViewConfigurationUpdateForm value, ConstraintValidatorContext context) {
    context.disableDefaultConstraintViolation();
    var palette = view(value).map(View::viewClass).flatMap(paletteService::findPaletteView);
    if (palette.isEmpty()) {
      context
          .buildConstraintViolationWithTemplate("Palette not found for view")
          .addConstraintViolation();
      return false;
    }

    return validateFieldNames(palette.get(), value.getColumnDefinitionGroups(), context);
  }

  private boolean validateFieldNames(
      PaletteView<?> defaultView,
      List<ColumnDefinitionGroupForm> columnDefinitionGroups,
      ConstraintValidatorContext context) {
    if (CollectionUtils.isEmpty(columnDefinitionGroups)) {
      return true;
    }
    var validFieldNames =
        defaultView.fieldDefinitions().stream()
            .map(FieldDefinitionView::name)
            .collect(Collectors.toSet());

    var unknownFields =
        columnDefinitionGroups.stream()
            .map(ColumnDefinitionGroupForm::columnDefinitions)
            .flatMap(Collection::stream)
            .map(ColumnDefinitionForm::fieldName)
            .filter(not(validFieldNames::contains))
            .toList();

    if (!unknownFields.isEmpty()) {
      context
          .buildConstraintViolationWithTemplate(
              "Unknown field names: " + String.join(", ", unknownFields))
          .addConstraintViolation();
    }

    return unknownFields.isEmpty();
  }

  private Optional<View<?>> view(ViewConfigurationUpdateForm updateForm) {
    if (updateForm instanceof ViewConfigurationCreateForm createForm) {
      return Optional.of(createForm.getScope());
    }

    return existingViewId().flatMap(repository::findById).map(ViewConfiguration::getScope);
  }

  private Optional<ObjectId> existingViewId() {
    return Checked.now(() -> new ObjectId(pathVariablesSupport.getPathVariable("viewId")))
        .toOptional();
  }
}
