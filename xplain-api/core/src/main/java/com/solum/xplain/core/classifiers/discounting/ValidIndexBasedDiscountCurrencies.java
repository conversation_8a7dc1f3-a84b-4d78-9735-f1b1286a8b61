package com.solum.xplain.core.classifiers.discounting;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = ValidIndexBasedDiscountCurrenciesValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ValidIndexBasedDiscountCurrencies {
  String message() default
      "{com.solum.xplain.api.classifiers.discounting.ValidIndexBasedDiscountCurrencies.message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
