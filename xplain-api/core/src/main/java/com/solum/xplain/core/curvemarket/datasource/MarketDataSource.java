package com.solum.xplain.core.curvemarket.datasource;

import com.solum.xplain.core.curvemarket.MarketDataExtractionParams;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketData;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValue;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueMapperFunction;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.Map;

public interface MarketDataSource {

  Either<ErrorItem, CalculationMarketData> provide(MarketDataExtractionParams mdParams);

  <T extends CalculationMarketValue> Map<String, T> provideMarketData(
      MarketDataExtractionParams mdParams, CalculationMarketValueMapperFunction<T> toValue);
}
