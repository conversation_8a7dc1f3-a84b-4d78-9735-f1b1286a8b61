package com.solum.xplain.core.classifiers.csv;

import static com.solum.xplain.core.common.csv.CsvColumn.textObject;

import com.opengamma.strata.product.swap.type.XCcyOvernightOvernightSwapConvention;
import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import java.util.List;
import org.springframework.core.io.ByteArrayResource;

public class XCcyOvernightOvernightSwapConventionMapper
    extends CsvMapper<XCcyOvernightOvernightSwapConvention> {

  private static final List<CsvColumn<XCcyOvernightOvernightSwapConvention>> COLUMNS =
      List.of(
          textObject("name", XCcyOvernightOvernightSwapConvention::getName),
          textObject("spotDateOffset", XCcyOvernightOvernightSwapConvention::getSpotDateOffset),
          textObject("flatLeg.index", x -> x.getFlatLeg().getIndex()),
          textObject("flatLeg.currency", x -> x.getFlatLeg().getCurrency()),
          textObject("flatLeg.dayCount", x -> x.getFlatLeg().getDayCount()),
          textObject("flatLeg.accrual", x -> x.getFlatLeg().getAccrualMethod()),
          textObject("flatLeg.accrualFreq", x -> x.getFlatLeg().getAccrualFrequency()),
          textObject("flatLeg.accrualBDA", x -> x.getFlatLeg().getAccrualBusinessDayAdjustment()),
          textObject(
              "flatLeg.startDateBDA", x -> x.getFlatLeg().getStartDateBusinessDayAdjustment()),
          textObject("flatLeg.endDateBDA", x -> x.getFlatLeg().getEndDateBusinessDayAdjustment()),
          textObject("flatLeg.stubConv", x -> x.getFlatLeg().getStubConvention()),
          textObject("flatLeg.rollConv", x -> x.getFlatLeg().getRollConvention()),
          textObject("flatLeg.payFreq", x -> x.getFlatLeg().getPaymentFrequency()),
          textObject("flatLeg.payDateOffset", x -> x.getFlatLeg().getPaymentDateOffset()),
          textObject("flatLeg.compounding", x -> x.getFlatLeg().getCompoundingMethod()),
          textObject("flatLeg.rateCutOffDays", x -> x.getFlatLeg().getRateCutOffDays()),
          textObject("flatLeg.notionalExc", x -> x.getFlatLeg().isNotionalExchange()),
          textObject("spreadLeg.index", x -> x.getSpreadLeg().getIndex()),
          textObject("spreadLeg.currency", x -> x.getSpreadLeg().getCurrency()),
          textObject("spreadLeg.dayCount", x -> x.getSpreadLeg().getDayCount()),
          textObject("spreadLeg.accrualFreq", x -> x.getSpreadLeg().getAccrualFrequency()),
          textObject("spreadLeg.accrual", x -> x.getSpreadLeg().getAccrualMethod()),
          textObject(
              "spreadLeg.accrualBDA", x -> x.getSpreadLeg().getAccrualBusinessDayAdjustment()),
          textObject(
              "spreadLeg.startDateBDA", x -> x.getSpreadLeg().getStartDateBusinessDayAdjustment()),
          textObject(
              "spreadLeg.endDateBDA", x -> x.getSpreadLeg().getEndDateBusinessDayAdjustment()),
          textObject("spreadLeg.stubConv", x -> x.getSpreadLeg().getStubConvention()),
          textObject("spreadLeg.rollConv", x -> x.getSpreadLeg().getRollConvention()),
          textObject("spreadLeg.payFreq", x -> x.getSpreadLeg().getPaymentFrequency()),
          textObject("spreadLeg.payDateOffset", x -> x.getSpreadLeg().getPaymentDateOffset()),
          textObject("spreadLeg.compounding", x -> x.getSpreadLeg().getCompoundingMethod()),
          textObject("spreadLeg.rateCutOffDays", x -> x.getSpreadLeg().getRateCutOffDays()),
          textObject("spreadLeg.notionalExc", x -> x.getSpreadLeg().isNotionalExchange()));

  private XCcyOvernightOvernightSwapConventionMapper() {
    super(COLUMNS, null);
  }

  public static ByteArrayResource xccyOvernightOvernightSwapConventionsCsv() {
    return new ConventionalTradeConventionExporter<>(
            new XCcyOvernightOvernightSwapConventionMapper(),
            XCcyOvernightOvernightSwapConvention.class)
        .export();
  }
}
