package com.solum.xplain.core.curvegroup.conventions.index;

import static com.opengamma.strata.basics.index.IborIndices.AUD_BBSW_1M;
import static com.opengamma.strata.basics.index.IborIndices.AUD_BBSW_3M;
import static com.opengamma.strata.basics.index.IborIndices.CAD_CDOR_1M;
import static com.opengamma.strata.basics.index.IborIndices.CHF_LIBOR_1M;
import static com.opengamma.strata.basics.index.IborIndices.CHF_LIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.CHF_LIBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.CZK_PRIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.DKK_CIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_12M;
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_1M;
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.EUR_EURIBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.GBP_LIBOR_12M;
import static com.opengamma.strata.basics.index.IborIndices.GBP_LIBOR_1M;
import static com.opengamma.strata.basics.index.IborIndices.GBP_LIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.GBP_LIBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.HUF_BUBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.JPY_LIBOR_1M;
import static com.opengamma.strata.basics.index.IborIndices.JPY_LIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.NOK_NIBOR_3M;
import static com.opengamma.strata.basics.index.IborIndices.SEK_STIBOR_6M;
import static com.opengamma.strata.basics.index.IborIndices.USD_LIBOR_1M;
import static com.opengamma.strata.basics.index.IborIndices.USD_LIBOR_6M;
import static com.opengamma.strata.basics.index.OvernightIndices.CAD_CORRA;
import static com.opengamma.strata.basics.index.OvernightIndices.EUR_EONIA;
import static com.opengamma.strata.basics.index.OvernightIndices.EUR_ESTR;
import static com.opengamma.strata.basics.index.OvernightIndices.GBP_SONIA;
import static com.opengamma.strata.basics.index.OvernightIndices.USD_FED_FUND;
import static com.opengamma.strata.basics.index.OvernightIndices.USD_SOFR;
import static com.opengamma.strata.product.index.type.IborFutureContractSpecs.EUR_EURIBOR_3M_IMM_ICE;
import static com.opengamma.strata.product.index.type.IborFutureContractSpecs.GBP_LIBOR_3M_IMM_ICE;
import static com.opengamma.strata.product.swap.type.IborIborSwapConventions.JPY_LIBOR_1M_LIBOR_6M;
import static com.opengamma.strata.product.swap.type.IborIborSwapConventions.JPY_LIBOR_3M_LIBOR_6M;
import static com.opengamma.strata.product.swap.type.IborIborSwapConventions.USD_LIBOR_1M_LIBOR_3M;
import static com.opengamma.strata.product.swap.type.IborIborSwapConventions.USD_LIBOR_3M_LIBOR_6M;
import static com.opengamma.strata.product.swap.type.OvernightIborSwapConventions.GBP_SONIA_OIS_1Y_LIBOR_3M;
import static com.opengamma.strata.product.swap.type.OvernightIborSwapConventions.USD_FED_FUND_AA_LIBOR_3M;
import static com.solum.xplain.core.curvegroup.conventions.index.ImmutableIndexBasisCurveConvention.indexCurve;
import static com.solum.xplain.extensions.overnightswap.OvernightTermOvernightSwapConventions.USD_SOFR_OIS_SOFR_3M;
import static com.solum.xplain.extensions.overnightswap.OvernightTermOvernightSwapConventions.USD_SOFR_OIS_SOFR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.AED_EIBOR_1M_EIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.AED_EIBOR_3M_EIBOR_12M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.AED_EIBOR_3M_EIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.AUD_BBSW_1M_BBSW_3M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.AUD_BBSW_3M_BBSW_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.CAD_CDOR_1M_CDOR_3M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.CHF_LIBOR_1M_LIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.CHF_LIBOR_3M_LIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.CZK_PRIBOR_3M_PRIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.DKK_CIBOR_3M_CIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.EUR_EURIBOR_1M_EURIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.EUR_EURIBOR_3M_EURIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.EUR_EURIBOR_6M_EURIBOR_12M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.GBP_LIBOR_1M_LIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.GBP_LIBOR_3M_LIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.GBP_LIBOR_6M_LIBOR_12M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.HUF_BUBOR_3M_BUBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.NOK_NIBOR_3M_NIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.SEK_STIBOR_3M_STIBOR_6M;
import static com.solum.xplain.extensions.product.ExtendedIborIborSwapConventions.SGD_SOR_1M_SOR_6M;
import static com.solum.xplain.extensions.product.ExtendedOvernightIborSwapConventions.CAD_CORRA_OIS_6M_CDOR_3M;
import static com.solum.xplain.extensions.product.ExtendedOvernightIborSwapConventions.EUR_EONIA_OIS_1Y_EURIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedOvernightIborSwapConventions.EUR_ESTR_OIS_1Y_EURIBOR_3M;
import static com.solum.xplain.extensions.product.ExtendedOvernightIborSwapConventions.USD_SOFR_OIS_3M_LIBOR;
import static com.solum.xplain.extensions.product.ExtendedTermDepositConventions.AUD_DEPOSIT_T0;
import static com.solum.xplain.extensions.product.ExtendedTermDepositConventions.AUD_DEPOSIT_T1;
import static com.solum.xplain.extensions.product.ExtendedTermDepositConventions.AUD_DEPOSIT_T2;
import static com.solum.xplain.extensions.termdeposit.TermOisFixingDepositConventions.USD_SOFR_3M_TERM_OIS;
import static com.solum.xplain.extensions.termdeposit.TermOisFixingDepositConventions.USD_SOFR_6M_TERM_OIS;
import static java.util.List.of;

import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.product.deposit.type.IborFixingDepositConvention;
import com.opengamma.strata.product.fra.type.FraConvention;
import com.opengamma.strata.product.index.type.IborFutureContractSpec;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConvention;
import com.solum.xplain.extensions.index.OvernightTermIndex;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class IndexBasisCurveConventions {
  private static final IborIndex AED_EIBOR_1M = IborIndex.of("AED-EIBOR-1M");
  private static final IborIndex AED_EIBOR_12M = IborIndex.of("AED-EIBOR-12M");
  private static final IborIndex AED_EIBOR_6M = IborIndex.of("AED-EIBOR-6M");
  private static final IborIndex SGD_SOR_1M = IborIndex.of("SGD-SOR-1M");
  private static final IborIndex SGD_SOR_6M = IborIndex.of("SGD-SOR-6M");

  private static final OvernightIndex USD_SOFR_3M_INDEX = OvernightTermIndex.of("USD-SOFR-3M");
  private static final OvernightIndex USD_SOFR_6M_INDEX = OvernightTermIndex.of("USD-SOFR-6M");
  // AED
  private static final ImmutableIndexBasisCurveConvention AED_1M =
      indexCurve(
          "AED 1M",
          AED_EIBOR_1M,
          AED_EIBOR_1M_EIBOR_3M,
          IborFixingDepositConvention.of(AED_EIBOR_1M));

  private static final ImmutableIndexBasisCurveConvention AED_6M =
      indexCurve(
          "AED 6M",
          AED_EIBOR_6M,
          AED_EIBOR_3M_EIBOR_6M,
          IborFixingDepositConvention.of(AED_EIBOR_6M));

  private static final ImmutableIndexBasisCurveConvention AED_12M =
      indexCurve(
          "AED 12M",
          AED_EIBOR_12M,
          AED_EIBOR_3M_EIBOR_12M,
          IborFixingDepositConvention.of(AED_EIBOR_12M));

  // AUD
  private static final ImmutableIndexBasisCurveConvention AUD_1M =
      indexCurve(
          "AUD 1M",
          AUD_BBSW_1M,
          AUD_BBSW_1M_BBSW_3M,
          AUD_DEPOSIT_T0,
          AUD_DEPOSIT_T1,
          AUD_DEPOSIT_T2,
          IborFixingDepositConvention.of(AUD_BBSW_1M));

  private static final ImmutableIndexBasisCurveConvention AUD_3M =
      indexCurve(
          "AUD 3M",
          AUD_BBSW_3M,
          AUD_BBSW_1M_BBSW_3M,
          AUD_BBSW_3M_BBSW_6M,
          AUD_DEPOSIT_T0,
          AUD_DEPOSIT_T1,
          AUD_DEPOSIT_T2,
          IborFixingDepositConvention.of(AUD_BBSW_3M));

  // CAD
  private static final ImmutableIndexBasisCurveConvention CAD_1M =
      indexCurve(
          "CAD 1M", CAD_CDOR_1M, CAD_CDOR_1M_CDOR_3M, IborFixingDepositConvention.of(CAD_CDOR_1M));

  // CAD
  private static final ImmutableIndexBasisCurveConvention CAD_OIS =
      indexCurve("CAD CORRA", CAD_CORRA, CAD_CORRA_OIS_6M_CDOR_3M);

  // CHF
  private static final ImmutableIndexBasisCurveConvention CHF_1M =
      indexCurve(
          "CHF 1M",
          CHF_LIBOR_1M,
          CHF_LIBOR_1M_LIBOR_6M,
          IborFixingDepositConvention.of(CHF_LIBOR_1M));

  private static final ImmutableIndexBasisCurveConvention CHF_3M =
      indexCurve(
          "CHF 3M",
          CHF_LIBOR_3M,
          CHF_LIBOR_3M_LIBOR_6M,
          IborFutureContractSpec.of("CHF-LIBOR-3M-IMM-ICE"),
          IborFixingDepositConvention.of(CHF_LIBOR_3M));

  private static final ImmutableIndexBasisCurveConvention CHF_6M =
      indexCurve(
          "CHF 6M",
          CHF_LIBOR_6M,
          CHF_LIBOR_1M_LIBOR_6M,
          CHF_LIBOR_3M_LIBOR_6M,
          FraConvention.of(CHF_LIBOR_6M),
          IborFixingDepositConvention.of(CHF_LIBOR_6M));

  // CZK
  private static final ImmutableIndexBasisCurveConvention CZK_3M =
      indexCurve(
          "CZK 3M",
          CZK_PRIBOR_3M,
          CZK_PRIBOR_3M_PRIBOR_6M,
          FraConvention.of(CZK_PRIBOR_3M),
          IborFixingDepositConvention.of(CZK_PRIBOR_3M));

  // DKK
  private static final ImmutableIndexBasisCurveConvention DKK_3M =
      indexCurve(
          "DKK 3M",
          DKK_CIBOR_3M,
          DKK_CIBOR_3M_CIBOR_6M,
          IborFixingDepositConvention.of(DKK_CIBOR_3M));

  // EUR
  private static final ImmutableIndexBasisCurveConvention EUR_EONIA_OIS =
      indexCurve("EUR EONIA", EUR_EONIA, EUR_EONIA_OIS_1Y_EURIBOR_3M);

  private static final ImmutableIndexBasisCurveConvention EUR_ESTR_OIS =
      indexCurve("EUR ESTR", EUR_ESTR, EUR_ESTR_OIS_1Y_EURIBOR_3M);

  private static final ImmutableIndexBasisCurveConvention EUR_1M =
      indexCurve(
          "EUR 1M",
          EUR_EURIBOR_1M,
          EUR_EURIBOR_1M_EURIBOR_3M,
          IborFixingDepositConvention.of(EUR_EURIBOR_1M));

  private static final ImmutableIndexBasisCurveConvention EUR_3M =
      indexCurve(
          "EUR 3M",
          EUR_EURIBOR_3M,
          EUR_EURIBOR_1M_EURIBOR_3M,
          EUR_EURIBOR_3M_EURIBOR_6M,
          EUR_EURIBOR_3M_IMM_ICE,
          IborFixingDepositConvention.of(EUR_EURIBOR_3M));

  private static final ImmutableIndexBasisCurveConvention EUR_6M =
      indexCurve(
          "EUR 6M",
          EUR_EURIBOR_6M,
          EUR_EURIBOR_3M_EURIBOR_6M,
          EUR_EURIBOR_6M_EURIBOR_12M,
          FraConvention.of(EUR_EURIBOR_6M),
          IborFixingDepositConvention.of(EUR_EURIBOR_6M));

  private static final ImmutableIndexBasisCurveConvention EUR_12M =
      indexCurve(
          "EUR 12M",
          EUR_EURIBOR_12M,
          EUR_EURIBOR_6M_EURIBOR_12M,
          IborFixingDepositConvention.of(EUR_EURIBOR_12M));

  // GBP
  private static final ImmutableIndexBasisCurveConvention GBP_OIS =
      indexCurve("GBP SONIA", GBP_SONIA, GBP_SONIA_OIS_1Y_LIBOR_3M);

  private static final ImmutableIndexBasisCurveConvention GBP_1M =
      indexCurve(
          "GBP 1M",
          GBP_LIBOR_1M,
          GBP_LIBOR_1M_LIBOR_3M,
          IborFixingDepositConvention.of(GBP_LIBOR_1M));

  private static final ImmutableIndexBasisCurveConvention GBP_3M =
      indexCurve(
          "GBP 3M",
          GBP_LIBOR_3M,
          GBP_LIBOR_3M_LIBOR_6M,
          GBP_LIBOR_1M_LIBOR_3M,
          GBP_LIBOR_3M_IMM_ICE,
          IborFixingDepositConvention.of(GBP_LIBOR_3M));

  private static final ImmutableIndexBasisCurveConvention GBP_6M =
      indexCurve(
          "GBP 6M",
          GBP_LIBOR_6M,
          GBP_LIBOR_3M_LIBOR_6M,
          GBP_LIBOR_6M_LIBOR_12M,
          FraConvention.of(GBP_LIBOR_6M),
          IborFixingDepositConvention.of(GBP_LIBOR_6M));

  private static final ImmutableIndexBasisCurveConvention GBP_12M =
      indexCurve(
          "GBP 12M",
          GBP_LIBOR_12M,
          GBP_LIBOR_6M_LIBOR_12M,
          IborFixingDepositConvention.of(GBP_LIBOR_12M));

  // HUF
  private static final ImmutableIndexBasisCurveConvention HUF_3M =
      indexCurve(
          "HUF 3M",
          HUF_BUBOR_3M,
          HUF_BUBOR_3M_BUBOR_6M,
          FraConvention.of(HUF_BUBOR_3M),
          IborFixingDepositConvention.of(HUF_BUBOR_3M));

  // JPY
  private static final ImmutableIndexBasisCurveConvention JPY_1M =
      indexCurve(
          "JPY 1M",
          JPY_LIBOR_1M,
          JPY_LIBOR_1M_LIBOR_6M,
          IborFixingDepositConvention.of(JPY_LIBOR_1M));

  private static final ImmutableIndexBasisCurveConvention JPY_3M =
      indexCurve(
          "JPY 3M",
          JPY_LIBOR_3M,
          JPY_LIBOR_3M_LIBOR_6M,
          IborFixingDepositConvention.of(JPY_LIBOR_3M));

  // NOK
  private static final ImmutableIndexBasisCurveConvention NOK_3M =
      indexCurve(
          "NOK 3M",
          NOK_NIBOR_3M,
          NOK_NIBOR_3M_NIBOR_6M,
          IborFixingDepositConvention.of(NOK_NIBOR_3M));

  // SEK
  private static final ImmutableIndexBasisCurveConvention SEK_6M =
      indexCurve(
          "SEK 6M",
          SEK_STIBOR_6M,
          SEK_STIBOR_3M_STIBOR_6M,
          IborFixingDepositConvention.of(SEK_STIBOR_6M));

  // SGD
  private static final ImmutableIndexBasisCurveConvention SGD_1M =
      indexCurve(
          "SGD 1M", SGD_SOR_1M, SGD_SOR_1M_SOR_6M, IborFixingDepositConvention.of(SGD_SOR_1M));

  private static final ImmutableIndexBasisCurveConvention SGD_6M =
      indexCurve(
          "SGD 6M", SGD_SOR_6M, SGD_SOR_1M_SOR_6M, IborFixingDepositConvention.of(SGD_SOR_6M));

  // USD
  private static final ImmutableIndexBasisCurveConvention USD_SOFR_OIS =
      indexCurve("USD SOFR", USD_SOFR, USD_SOFR_OIS_3M_LIBOR);

  private static final ImmutableIndexBasisCurveConvention USD_FEDFUNDS_OIS =
      indexCurve("USD FEDFUNDS", USD_FED_FUND, USD_FED_FUND_AA_LIBOR_3M);

  private static final ImmutableIndexBasisCurveConvention USD_1M =
      indexCurve(
          "USD 1M",
          USD_LIBOR_1M,
          USD_LIBOR_1M_LIBOR_3M,
          IborFixingDepositConvention.of(USD_LIBOR_1M));

  private static final ImmutableIndexBasisCurveConvention USD_6M =
      indexCurve(
          "USD 6M",
          USD_LIBOR_6M,
          USD_LIBOR_3M_LIBOR_6M,
          IborFixingDepositConvention.of(USD_LIBOR_6M));

  private static final ImmutableIndexBasisCurveConvention USD_SOFR_3M =
      indexCurve("USD SOFR 3M", USD_SOFR_3M_INDEX, USD_SOFR_3M_TERM_OIS, USD_SOFR_OIS_SOFR_3M);

  private static final ImmutableIndexBasisCurveConvention USD_SOFR_6M =
      indexCurve("USD SOFR 6M", USD_SOFR_6M_INDEX, USD_SOFR_6M_TERM_OIS, USD_SOFR_OIS_SOFR_6M);

  public static final List<ConventionalCurveConvention> INDEX_BASIS_CURVES =
      of(
          AED_1M,
          AED_6M,
          AED_12M,
          AUD_1M,
          AUD_3M,
          CAD_1M,
          CAD_OIS,
          CHF_1M,
          CHF_3M,
          CHF_6M,
          CZK_3M,
          DKK_3M,
          EUR_1M,
          EUR_3M,
          EUR_6M,
          EUR_12M,
          EUR_EONIA_OIS,
          EUR_ESTR_OIS,
          GBP_1M,
          GBP_3M,
          GBP_6M,
          GBP_12M,
          GBP_OIS,
          HUF_3M,
          JPY_1M,
          JPY_3M,
          NOK_3M,
          SEK_6M,
          SGD_1M,
          SGD_6M,
          USD_1M,
          USD_6M,
          USD_FEDFUNDS_OIS,
          USD_SOFR_OIS,
          USD_SOFR_3M,
          USD_SOFR_6M);
}
