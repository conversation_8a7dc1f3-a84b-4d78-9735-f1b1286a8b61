package com.solum.xplain.core.classifiers;

import static com.solum.xplain.core.classifiers.CdsIndex.CDX_NA_HY;
import static com.solum.xplain.core.classifiers.CdsIndex.ITRAXX_EUR_XOVER;
import static com.solum.xplain.core.classifiers.CreditTranches.TRANCHE_0_10;
import static com.solum.xplain.core.classifiers.CreditTranches.TRANCHE_0_15;
import static com.solum.xplain.core.classifiers.CreditTranches.TRANCHE_0_3;
import static com.solum.xplain.core.classifiers.CreditTranches.TRANCHE_10_20;
import static com.solum.xplain.core.classifiers.CreditTranches.TRANCHE_12_100;
import static com.solum.xplain.core.classifiers.CreditTranches.TRANCHE_15_100;
import static com.solum.xplain.core.classifiers.CreditTranches.TRANCHE_15_25;
import static com.solum.xplain.core.classifiers.CreditTranches.TRANCHE_20_35;
import static com.solum.xplain.core.classifiers.CreditTranches.TRANCHE_25_35;
import static com.solum.xplain.core.classifiers.CreditTranches.TRANCHE_35_100;
import static com.solum.xplain.core.classifiers.CreditTranches.TRANCHE_3_6;
import static com.solum.xplain.core.classifiers.CreditTranches.TRANCHE_3_7;
import static com.solum.xplain.core.classifiers.CreditTranches.TRANCHE_6_12;
import static com.solum.xplain.core.classifiers.CreditTranches.TRANCHE_7_15;

import com.google.common.collect.ImmutableMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CreditTrancheIndices {

  public static final Map<CdsIndex, List<String>> PERMISSIBLE_VALUES =
      ImmutableMap.<CdsIndex, List<String>>builder()
          .put(CDX_NA_HY, List.of(TRANCHE_0_15, TRANCHE_15_25, TRANCHE_25_35, TRANCHE_35_100))
          .put(CdsIndex.CDX_NA_IG, List.of(TRANCHE_0_3, TRANCHE_3_7, TRANCHE_7_15, TRANCHE_15_100))
          .put(CdsIndex.ITRAXX_EUR, List.of(TRANCHE_0_3, TRANCHE_3_6, TRANCHE_6_12, TRANCHE_12_100))
          .put(
              ITRAXX_EUR_XOVER,
              List.of(
                  TRANCHE_0_15,
                  TRANCHE_15_25,
                  TRANCHE_25_35,
                  TRANCHE_35_100,
                  TRANCHE_0_10,
                  TRANCHE_10_20,
                  TRANCHE_20_35))
          .build();

  public static final Set<String> ALL_TRANCHE_VALUES =
      PERMISSIBLE_VALUES.values().stream()
          .flatMap(List::stream)
          .collect(Collectors.toUnmodifiableSet());

  public static List<CdsIndex> permissibleIndicesForTranche(String tranche) {
    return PERMISSIBLE_VALUES.entrySet().stream()
        .filter(v -> v.getValue().contains(tranche))
        .map(Entry::getKey)
        .toList();
  }
}
