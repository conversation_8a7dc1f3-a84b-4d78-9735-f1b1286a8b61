package com.solum.xplain.core.curvemarket.node;

import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
public class NodeInstrumentWrapper<T> {
  private final T node;
  private final InstrumentDefinition definition;

  private NodeInstrumentWrapper(T node, InstrumentDefinition definition) {
    this.node = node;
    this.definition = definition;
  }

  public static <T> NodeInstrumentWrapper<T> of(T node, InstrumentDefinition definition) {
    return new NodeInstrumentWrapper<>(node, definition);
  }

  public T getNode() {
    return node;
  }

  public InstrumentDefinition getDefinition() {
    return definition;
  }
}
