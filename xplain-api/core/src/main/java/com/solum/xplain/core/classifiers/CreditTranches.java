package com.solum.xplain.core.classifiers;

import com.google.common.collect.ImmutableMap;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CreditTranches {
  public static final String TRANCHE_0_15 = "0-15";
  public static final String TRANCHE_0_10 = "0-10";
  public static final String TRANCHE_15_25 = "15-25";
  public static final String TRANCHE_10_20 = "10-20";
  public static final String TRANCHE_20_35 = "20-35";
  public static final String TRANCHE_25_35 = "25-35";
  public static final String TRANCHE_35_100 = "35-100";
  public static final String TRANCHE_0_3 = "0-3";
  public static final String TRANCHE_3_7 = "3-7";
  public static final String TRANCHE_7_15 = "7-15";
  public static final String TRANCHE_15_100 = "15-100";
  public static final String TRANCHE_3_6 = "3-6";
  public static final String TRANCHE_6_12 = "6-12";
  public static final String TRANCHE_12_100 = "12-100";

  private static final Map<String, String> CSV_VALUES_MAP =
      ImmutableMap.<String, String>builder()
          .put(TRANCHE_0_15, "0-0.15")
          .put(TRANCHE_0_3, "0-0.03")
          .put(TRANCHE_0_10, "0-0.10")
          .put(TRANCHE_12_100, "0.12-1.0")
          .put(TRANCHE_10_20, "0.10-0.20")
          .put(TRANCHE_15_25, "0.15-0.25")
          .put(TRANCHE_15_100, "0.15-1.0")
          .put(TRANCHE_25_35, "0.25-0.35")
          .put(TRANCHE_20_35, "0.20-0.35")
          .put(TRANCHE_35_100, "0.35-1.0")
          .put(TRANCHE_3_7, "0.03-0.07")
          .put(TRANCHE_7_15, "0.07-0.15")
          .put(TRANCHE_3_6, "0.03-0.06")
          .put(TRANCHE_6_12, "0.06-0.12")
          .build();

  private static final Map<String, String> CSV_LABELS_MAP =
      CSV_VALUES_MAP.entrySet().stream().collect(Collectors.toMap(Entry::getValue, Entry::getKey));

  @Nullable
  public static String formatTrancheLabel(String value) {
    return CSV_VALUES_MAP.get(value);
  }

  public static String parseFromLabel(String trancheLabel) {
    CsvLoaderUtils.validateValue(trancheLabel, CSV_LABELS_MAP.keySet());
    return CSV_LABELS_MAP.get(trancheLabel);
  }
}
