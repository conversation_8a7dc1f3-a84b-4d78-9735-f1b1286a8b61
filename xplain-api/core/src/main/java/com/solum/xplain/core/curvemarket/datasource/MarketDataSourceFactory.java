package com.solum.xplain.core.curvemarket.datasource;

import com.solum.xplain.core.calculationapi.OverlayMarketDataProvider;
import com.solum.xplain.core.calculationapi.PreliminaryMarketDataProvider;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import org.springframework.stereotype.Component;

@Component
public class MarketDataSourceFactory {
  private final RawMarketDataSource rawMarketDataProvider;
  private final PreliminaryMarketDataProvider preliminaryMarketDataProvider;
  private final OverlayMarketDataProvider overlayMarketDataProvider;

  public MarketDataSourceFactory(
      RawMarketDataSource rawMarketDataProvider,
      PreliminaryMarketDataProvider preliminaryMarketDataProvider,
      OverlayMarketDataProvider overlayMarketDataProvider) {
    this.rawMarketDataProvider = rawMarketDataProvider;
    this.preliminaryMarketDataProvider = preliminaryMarketDataProvider;
    this.overlayMarketDataProvider = overlayMarketDataProvider;
  }

  public Either<ErrorItem, MarketDataSource> get(MarketDataSourceType source) {
    return switch (source) {
      case RAW_PRIMARY, RAW_SECONDARY -> Either.right(rawMarketDataProvider);
      case PRELIMINARY_PRIMARY, PRELIMINARY_SECONDARY ->
          Either.right(preliminaryMarketDataProvider);
      case OVERLAY -> Either.right(overlayMarketDataProvider);
    };
  }
}
