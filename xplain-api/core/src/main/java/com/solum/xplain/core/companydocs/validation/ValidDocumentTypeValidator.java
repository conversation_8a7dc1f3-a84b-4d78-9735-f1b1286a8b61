package com.solum.xplain.core.companydocs.validation;

import static org.apache.tika.metadata.TikaCoreProperties.RESOURCE_NAME_KEY;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.io.IOException;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.config.TikaConfig;
import org.apache.tika.io.TikaInputStream;
import org.apache.tika.metadata.Metadata;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

@Component
@Slf4j
public class ValidDocumentTypeValidator
    implements ConstraintValidator<ValidDocumentType, MultipartFile> {

  private static final String CONTENT_TYPE_MATCH_FAILED_MESSAGE =
      "{com.solum.xplain.api.companydocs.validation.ValidDocumentType.contentTypeNoMatch}";
  private static final List<String> VALID_CONTENT_TYPES =
      List.of(
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "application/vnd.ms-excel",
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          "application/vnd.ms-powerpoint",
          "application/vnd.openxmlformats-officedocument.presentationml.presentation",
          "application/vnd.oasis.opendocument.text",
          "application/vnd.oasis.opendocument.presentation",
          "application/vnd.oasis.opendocument.spreadsheet",
          "application/pdf",
          "text/plain",
          "text/csv",
          "image/png",
          "image/jpeg");

  private final boolean contentTypeDetectionEnabled;

  private String fieldName;

  public ValidDocumentTypeValidator(
      @Value("${app.company-docs.advanced-content-type-verification-enabled:true}")
          boolean contentTypeDetectionEnabled) {
    this.contentTypeDetectionEnabled = contentTypeDetectionEnabled;
  }

  @Override
  public void initialize(ValidDocumentType constraintAnnotation) {
    this.fieldName = constraintAnnotation.fieldName();
  }

  @Override
  public boolean isValid(MultipartFile value, ConstraintValidatorContext context) {
    if (value == null) {
      return true;
    }
    if (!VALID_CONTENT_TYPES.contains(value.getContentType())) {
      return false;
    }
    var contentTypeMatches = !contentTypeDetectionEnabled || fileMatchesContentType(value);
    if (!contentTypeMatches) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate(CONTENT_TYPE_MATCH_FAILED_MESSAGE)
          .addPropertyNode(fieldName)
          .addConstraintViolation();
    }
    return contentTypeMatches;
  }

  private boolean fileMatchesContentType(MultipartFile value) {
    try (TikaInputStream inputStream = TikaInputStream.get(value.getBytes())) {
      Metadata md = new Metadata();
      md.set(RESOURCE_NAME_KEY, value.getOriginalFilename());

      var tikaDetector = TikaConfig.getDefaultConfig().getDetector();
      var detectedMediaType = tikaDetector.detect(inputStream, md);
      var contentTypeMatches = detectedMediaType.toString().equals(value.getContentType());
      if (!contentTypeMatches) {
        log.warn(
            "Provided content type {} does not match detected content type {}",
            value.getContentType(),
            detectedMediaType);
      }
      return contentTypeMatches;
    } catch (IOException e) {
      log.warn("Error while detecting content type: ", e);
      return true;
    }
  }
}
