package com.solum.xplain.core.curvemarket.marketvalue;

import static com.solum.xplain.core.market.validation.ValidFxRateKeyValidator.isValidKey;
import static java.util.Optional.ofNullable;

import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType;
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@Builder
@FieldNameConstants
public class CalculationMarketData {
  private String curveConfigurationId;
  private String marketDataGroupId;
  private MarketDataSourceType marketDataSourceType;
  private String name;
  private List<CalculationMarketValueView> values;

  public List<CalculationMarketValueView> nonFxValues() {
    return nonNullValues()
        .filter(v -> !isValidKey(v.getKey(), CoreInstrumentType.FX_RATE.name()))
        .toList();
  }

  public List<CalculationMarketValueView> fxRates() {
    return nonNullValues()
        .filter(v -> isValidKey(v.getKey(), CoreInstrumentType.FX_RATE.name()))
        .toList();
  }

  private Stream<CalculationMarketValueView> nonNullValues() {
    return Stream.ofNullable(values)
        .flatMap(Collection::stream)
        .filter(v -> Objects.nonNull(v.getValue()));
  }

  public CalculationMarketData applyOverlay(
      Map<String, Map<ValueBidAskType, BigDecimal>> overlayValues) {
    var overlaid =
        values.stream()
            .flatMap(
                v -> ofNullable(overlayValues.get(v.getKey())).map(v::copyWithOverlay).stream())
            .toList();

    return CalculationMarketData.builder()
        .values(overlaid)
        .curveConfigurationId(curveConfigurationId)
        .marketDataGroupId(marketDataGroupId)
        .name(name)
        .marketDataSourceType(marketDataSourceType)
        .build();
  }
}
