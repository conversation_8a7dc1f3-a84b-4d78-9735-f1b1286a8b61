package com.solum.xplain.core.classifiers.csv;

import static com.solum.xplain.core.common.csv.CsvColumn.textObject;

import com.opengamma.strata.product.fx.type.FxSwapConvention;
import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import java.util.List;
import org.springframework.core.io.ByteArrayResource;

public class FxSwapConventionMapper extends CsvMapper<FxSwapConvention> {

  private static final List<CsvColumn<FxSwapConvention>> COLUMNS =
      List.of(
          textObject("name", FxSwapConvention::getName),
          textObject("baseCcy", x -> x.getCurrencyPair().getBase()),
          textObject("counterCcy", x -> x.getCurrencyPair().getCounter()),
          textObject("spotDateOffset", FxSwapConvention::getSpotDateOffset));

  private FxSwapConventionMapper() {
    super(COLUMNS, null);
  }

  public static ByteArrayResource fxSwapConventionsCsv() {
    return new ConventionalTradeConventionExporter<>(
            new FxSwapConventionMapper(), FxSwapConvention.class)
        .export();
  }
}
