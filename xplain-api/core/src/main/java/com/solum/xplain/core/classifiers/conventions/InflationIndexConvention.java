package com.solum.xplain.core.classifiers.conventions;

import com.opengamma.strata.product.swap.type.InflationRateSwapLegConvention;
import com.solum.xplain.core.utils.FrequencyUtils;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class InflationIndexConvention {
  private final String name;
  private final String currency;
  private final String lag;
  private final String calculationMethod;

  public static InflationIndexConvention newOf(InflationRateSwapLegConvention convention) {
    return new InflationIndexConvention(
        convention.getIndex().getName(),
        ConventionMapper.INSTANCE.map(convention.getCurrency()),
        FrequencyUtils.toStringNoPrefix(convention.getLag()),
        convention.getIndexCalculationMethod().toString());
  }
}
