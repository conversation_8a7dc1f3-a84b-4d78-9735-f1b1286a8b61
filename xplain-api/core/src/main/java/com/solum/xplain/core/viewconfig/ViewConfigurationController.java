package com.solum.xplain.core.viewconfig;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_VIEW_CONFIGURATIONS;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;

import com.solum.xplain.core.authentication.NoRoleAuthorization;
import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EitherResultOrErrorResponseEntity;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.validation.ValidObjectId;
import com.solum.xplain.core.viewconfig.provider.PaletteService;
import com.solum.xplain.core.viewconfig.provider.ViewConfigurationService;
import com.solum.xplain.core.viewconfig.value.PaletteView;
import com.solum.xplain.core.viewconfig.value.View;
import com.solum.xplain.core.viewconfig.value.ViewConfigurationCreateForm;
import com.solum.xplain.core.viewconfig.value.ViewConfigurationUpdateForm;
import com.solum.xplain.core.viewconfig.value.ViewConfigurationView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/viewconfig")
@RequiredArgsConstructor
public class ViewConfigurationController {
  private final ViewConfigurationService viewConfigurationService;
  private final PaletteService paletteService;

  @Operation(summary = "Create new view configuration for a view.")
  @PostMapping
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VIEW_CONFIGURATIONS)
  public EntityId createViewConfiguration(@Valid @RequestBody ViewConfigurationCreateForm form) {
    return viewConfigurationService.saveViewConfiguration(form);
  }

  @Operation(
      summary =
          "List all available view configurations for a view. If the view is not recognised then an error response is returned.")
  @GetMapping("/{view}")
  @CommonErrors
  @NoRoleAuthorization
  public <T> List<ViewConfigurationView<T>> list(
      @Parameter(
              description = "name of the configurable view",
              example = "IpvTradeOverlayResultView",
              required = true)
          @PathVariable
          View<T> view,
      Authentication user) {
    return viewConfigurationService.findAvailableViewConfigurations(user, view.viewClass());
  }

  @Operation(
      summary =
          "List palette of all available column definitions for a view. If the view is not recognised then an error response is returned.")
  @GetMapping("/{view}/palette")
  @CommonErrors
  @NoRoleAuthorization
  public <T> PaletteView<T> palette(
      @Parameter(
              description = "name of the configurable view",
              example = "IpvTradeOverlayResultView",
              required = true)
          @PathVariable
          View<T> view) {
    return paletteService
        .findPaletteView(view.viewClass())
        .orElseThrow(() -> new IllegalArgumentException("View not found"));
  }

  @Operation(
      summary = "Fetch a view configuration for a view by id.",
      description =
          "If the view configuration is not found then the default view configuration for the view is returned instead. If no default view configuration is available then an error response is returned.")
  @GetMapping("/{view}/{viewConfigId}")
  @CommonErrors
  @NoRoleAuthorization
  public <T> ResponseEntity<ViewConfigurationView<T>> fetchNamedOrDefault(
      @Parameter(
              description = "name of the configurable view",
              example = "InstrumentOverlayResultView",
              required = true)
          @PathVariable
          View<T> view,
      @Parameter(description = "unique id the view configuration", required = true) @PathVariable
          String viewConfigId,
      Authentication user) {
    return eitherErrorItemResponse(
        viewConfigurationService.getViewConfigurationOrDefault(
            user, view.viewClass(), viewConfigId));
  }

  @Operation(summary = "Update existing view configuration")
  @PutMapping("/{viewId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VIEW_CONFIGURATIONS)
  public ResponseEntity<EntityId> updateViewConfiguration(
      @PathVariable @ValidObjectId String viewId,
      @Valid @RequestBody ViewConfigurationUpdateForm form,
      Authentication user) {
    return EitherResultOrErrorResponseEntity.eitherErrorItemResponse(
        viewConfigurationService.updateViewConfiguration(user, viewId, form));
  }

  @Operation(summary = "Delete view configuration")
  @DeleteMapping("/{viewId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VIEW_CONFIGURATIONS)
  public ResponseEntity<EntityId> deleteViewConfiguration(
      @PathVariable @ValidObjectId String viewId, Authentication user) {
    return EitherResultOrErrorResponseEntity.eitherErrorItemResponse(
        viewConfigurationService.deleteViewConfiguration(user, viewId));
  }

  @Operation(summary = "Clone view configuration")
  @PutMapping("/{view}/{viewId}/clone")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VIEW_CONFIGURATIONS)
  public <T> ResponseEntity<EntityId> cloneViewConfiguration(
      @PathVariable String viewId, Authentication user, @PathVariable View<T> view) {
    return EitherResultOrErrorResponseEntity.eitherErrorItemResponse(
        viewConfigurationService.cloneViewConfiguration(user, viewId, view.viewClass()));
  }
}
