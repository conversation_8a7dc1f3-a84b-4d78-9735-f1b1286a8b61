package com.solum.xplain.core.classifiers.csv;

import static com.solum.xplain.core.common.csv.CsvColumn.namedObject;
import static com.solum.xplain.core.common.csv.CsvColumn.textObject;

import com.opengamma.strata.collect.named.Named;
import com.opengamma.strata.product.index.type.IborFutureContractSpec;
import com.opengamma.strata.product.index.type.ImmutableIborFutureContractSpec;
import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.extensions.product.IborFutureContractSpecWithFixingOffsetOverride;
import java.util.List;
import java.util.function.Function;
import org.springframework.core.io.ByteArrayResource;

public class IborFutureContractSpecMapper extends CsvMapper<IborFutureContractSpec> {

  private static final List<CsvColumn<IborFutureContractSpec>> COLUMNS =
      List.of(
          textObject("name", IborFutureContractSpec::getName),
          textObject("index", IborFutureContractSpec::getIndex),
          namedObject("dateSeq", dateSequenceExtractorFn()));

  private IborFutureContractSpecMapper() {
    super(COLUMNS, null);
  }

  public static ByteArrayResource iborFutureContractsCsv() {
    return new ConventionalTradeConventionExporter<>(
            new IborFutureContractSpecMapper(), IborFutureContractSpec.class)
        .export();
  }

  private static Function<IborFutureContractSpec, Named> dateSequenceExtractorFn() {
    return c -> {
      if (c instanceof ImmutableIborFutureContractSpec immutableIborFutureContractSpec) {
        return immutableIborFutureContractSpec.getDateSequence();
      } else if (c
          instanceof
          IborFutureContractSpecWithFixingOffsetOverride
              iborFutureContractSpecWithFixingOffsetOverride) {
        return iborFutureContractSpecWithFixingOffsetOverride.getDateSequence();
      }
      return null;
    };
  }
}
