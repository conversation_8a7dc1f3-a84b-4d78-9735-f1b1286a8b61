package com.solum.xplain.core.classifiers.discounting;

import com.opengamma.strata.basics.currency.Currency;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@AllArgsConstructor(staticName = "of")
public class IndexBasedDiscountCurrency {

  private final String name;
  private final Currency currency;

  public String getName() {
    return name;
  }

  public Currency getCurrency() {
    return currency;
  }
}
