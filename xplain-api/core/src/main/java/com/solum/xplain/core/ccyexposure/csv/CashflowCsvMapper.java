package com.solum.xplain.core.ccyexposure.csv;

import static com.solum.xplain.core.ccyexposure.csv.CcyExposureCsvFields.CASHFLOW_AMOUNT_FIELD;
import static com.solum.xplain.core.ccyexposure.csv.CcyExposureCsvFields.CASHFLOW_DATE_FIELD;
import static com.solum.xplain.core.ccyexposure.csv.CcyExposureCsvFields.CCY_EXPOSURE_NAME_FIELD;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.ccyexposure.value.CashflowView;
import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.core.common.csv.CsvRow;
import java.util.List;

public class CashflowCsvMapper extends CsvMapper<CashflowView> {

  private static final List<CsvColumn<CashflowView>> COLUMNS =
      List.of(
          CsvColumn.date(CashflowView.Fields.date, CASHFLOW_DATE_FIELD, CashflowView::getDate),
          CsvColumn.decimal(
              CashflowView.Fields.amount, CASHFLOW_AMOUNT_FIELD, CashflowView::getAmount));

  public CashflowCsvMapper() {
    super(COLUMNS, null);
  }

  @Override
  public List<String> header() {
    ImmutableList.Builder<String> builder = ImmutableList.builder();
    builder.add(CCY_EXPOSURE_NAME_FIELD);
    builder.addAll(super.header());
    return builder.build();
  }

  public CsvRow toCsvRow(String name, CashflowView cashflow) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    builder.add(new CsvField(CCY_EXPOSURE_NAME_FIELD, name));
    builder.addAll(super.toCsvFields(cashflow));
    return new CsvRow(builder.build());
  }
}
