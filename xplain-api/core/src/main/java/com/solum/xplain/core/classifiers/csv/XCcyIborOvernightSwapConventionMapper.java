package com.solum.xplain.core.classifiers.csv;

import static com.solum.xplain.core.common.csv.CsvColumn.textObject;

import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.extensions.xccyiborois.XCcyIborOvernightSwapConvention;
import java.util.List;
import org.springframework.core.io.ByteArrayResource;

public class XCcyIborOvernightSwapConventionMapper
    extends CsvMapper<XCcyIborOvernightSwapConvention> {

  private static final List<CsvColumn<XCcyIborOvernightSwapConvention>> COLUMNS =
      List.of(
          textObject("name", XCcyIborOvernightSwapConvention::getName),
          textObject("spotDateOffset", XCcyIborOvernightSwapConvention::getSpotDateOffset),
          textObject("iborLeg.index", x -> x.getIborLeg().getIndex()),
          textObject("iborLeg.currency", x -> x.getIborLeg().getCurrency()),
          textObject("iborLeg.dayCount", x -> x.getIborLeg().getDayCount()),
          textObject("iborLeg.accrualFreq", x -> x.getIborLeg().getAccrualFrequency()),
          textObject("iborLeg.accrualBDA", x -> x.getIborLeg().getAccrualBusinessDayAdjustment()),
          textObject(
              "iborLeg.startDateBDA", x -> x.getIborLeg().getStartDateBusinessDayAdjustment()),
          textObject("iborLeg.endDateBDA", x -> x.getIborLeg().getEndDateBusinessDayAdjustment()),
          textObject("iborLeg.stubConv", x -> x.getIborLeg().getStubConvention()),
          textObject("iborLeg.rollConv", x -> x.getIborLeg().getRollConvention()),
          textObject("iborLeg.payFreq", x -> x.getIborLeg().getPaymentFrequency()),
          textObject("iborLeg.payDateOffset", x -> x.getIborLeg().getPaymentDateOffset()),
          textObject("iborLeg.compounding", x -> x.getIborLeg().getCompoundingMethod()),
          textObject("iborLeg.notionalExc", x -> x.getIborLeg().isNotionalExchange()),
          textObject("overnightLeg.index", x -> x.getOvernightLeg().getIndex()),
          textObject("overnightLeg.currency", x -> x.getOvernightLeg().getCurrency()),
          textObject("overnightLeg.dayCount", x -> x.getOvernightLeg().getDayCount()),
          textObject("overnightLeg.accrualFreq", x -> x.getOvernightLeg().getAccrualFrequency()),
          textObject("overnightLeg.accrual", x -> x.getOvernightLeg().getAccrualMethod()),
          textObject(
              "overnightLeg.accrualBDA",
              x -> x.getOvernightLeg().getAccrualBusinessDayAdjustment()),
          textObject(
              "overnightLeg.startDateBDA",
              x -> x.getOvernightLeg().getStartDateBusinessDayAdjustment()),
          textObject(
              "overnightLeg.endDateBDA",
              x -> x.getOvernightLeg().getEndDateBusinessDayAdjustment()),
          textObject("overnightLeg.stubConv", x -> x.getOvernightLeg().getStubConvention()),
          textObject("overnightLeg.rollConv", x -> x.getOvernightLeg().getRollConvention()),
          textObject("overnightLeg.payFreq", x -> x.getOvernightLeg().getPaymentFrequency()),
          textObject("overnightLeg.payDateOffset", x -> x.getOvernightLeg().getPaymentDateOffset()),
          textObject("overnightLeg.compounding", x -> x.getOvernightLeg().getCompoundingMethod()),
          textObject("overnightLeg.rateCutOffDays", x -> x.getOvernightLeg().getRateCutOffDays()),
          textObject("overnightLeg.notionalExc", x -> x.getOvernightLeg().isNotionalExchange()));

  private XCcyIborOvernightSwapConventionMapper() {
    super(COLUMNS, null);
  }

  public static ByteArrayResource xccyIborOvernightSwapConventionsCsv() {
    return new ConventionalTradeConventionExporter<>(
            new XCcyIborOvernightSwapConventionMapper(), XCcyIborOvernightSwapConvention.class)
        .export();
  }
}
