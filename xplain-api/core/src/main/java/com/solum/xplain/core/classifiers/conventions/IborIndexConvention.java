package com.solum.xplain.core.classifiers.conventions;

import static com.solum.xplain.core.utils.FrequencyUtils.toStringNoPrefix;
import static com.solum.xplain.extensions.constants.IborIndexAccrualFrequencies.indexAccrualFrequency;

import com.opengamma.strata.basics.index.IborIndex;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class IborIndexConvention {
  private final String name;
  private final String tenor;
  private final String currency;
  private final String dayCount;
  private final String fixingCalendar;
  private final int fixingDateOffset;

  public static IborIndexConvention newOf(IborIndex iborIndex) {
    return new IborIndexConvention(
        iborIndex.getName(),
        toStringNoPrefix(indexAccrualFrequency(iborIndex)),
        ConventionMapper.INSTANCE.map(iborIndex.getCurrency()),
        ConventionMapper.INSTANCE.map(iborIndex.getDayCount()),
        ConventionMapper.INSTANCE.map(iborIndex.getFixingCalendar()),
        iborIndex.getFixingDateOffset().getDays());
  }
}
