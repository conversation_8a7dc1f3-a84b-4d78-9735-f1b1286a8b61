package com.solum.xplain.core.companydocs;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_COMPANY_DOCUMENTS;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_COMPANY_DOCUMENTS;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.lock.RequireLock.Type.PATH_VARIABLE;
import static com.solum.xplain.core.lock.XplainLock.COMPANY_LOCK_ID;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.FileUploadErrors;
import com.solum.xplain.core.companydocs.value.CompanyDocForm;
import com.solum.xplain.core.companydocs.value.CompanyDocView;
import com.solum.xplain.core.lock.RequireLock;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/companies/{companyId}/documents")
public class CompanyDocsController {

  private final CompanyDocsControllerService service;

  @Operation(summary = "Upload company document")
  @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_COMPANY_DOCUMENTS)
  @RequireLock(type = PATH_VARIABLE, name = "companyId", prefix = COMPANY_LOCK_ID)
  public ResponseEntity<EntityId> saveDocument(
      @PathVariable String companyId, @Validated CompanyDocForm docForm) {
    return eitherErrorItemResponse(service.saveDocument(companyId, docForm));
  }

  @Operation(summary = "Get company documents")
  @GetMapping
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_COMPANY_DOCUMENTS)
  public ResponseEntity<List<CompanyDocView>> companyDocuments(@PathVariable String companyId) {
    return eitherErrorItemResponse(service.companyDocuments(companyId));
  }

  @Operation(summary = "Download document")
  @GetMapping("/{documentId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_COMPANY_DOCUMENTS)
  public ResponseEntity<ByteArrayResource> downloadDocument(
      @PathVariable String companyId, @PathVariable String documentId) {
    return eitherErrorItemFileResponse(service.exportDocument(companyId, documentId));
  }

  @Operation(summary = "Archive document")
  @PutMapping("/{documentId}/archive")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_COMPANY_DOCUMENTS)
  public ResponseEntity<EntityId> archiveDocument(
      @PathVariable String companyId, @PathVariable String documentId) {
    return eitherErrorItemResponse(service.archiveDocument(companyId, documentId));
  }
}
