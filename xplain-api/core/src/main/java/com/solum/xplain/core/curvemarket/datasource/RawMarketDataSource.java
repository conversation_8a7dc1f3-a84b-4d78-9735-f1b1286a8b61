package com.solum.xplain.core.curvemarket.datasource;

import com.solum.xplain.core.curveconfiguration.CurveConfigurationMarketValueResolver;
import com.solum.xplain.core.curvemarket.MarketDataExtractionParams;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketData;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValue;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueMapperFunction;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.mdvalue.MarketDataValueService;
import io.atlassian.fugue.Either;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class RawMarketDataSource implements MarketDataSource {
  private final CurveConfigurationMarketValueResolver resolver;
  private final MarketDataValueService marketDataValueService;

  @Override
  public Either<ErrorItem, CalculationMarketData> provide(MarketDataExtractionParams mdParams) {
    return mdParams
        .resolvedValuesByKey(marketDataValueService)
        .flatMap(d -> resolver.curveConfigurationMarketData(mdParams, d));
  }

  @Override
  public <T extends CalculationMarketValue> Map<String, T> provideMarketData(
      MarketDataExtractionParams key, CalculationMarketValueMapperFunction<T> toValue) {
    return resolver
        .resolveMarketData(
            key.getStateDate(),
            key.getConfigurationId(),
            key.getPriceTypeResolver(),
            key.resolvedValuesByKey(marketDataValueService).getOrElse(Map.of()),
            toValue)
        .stream()
        .collect(
            Collectors.toMap(CalculationMarketValue::getKey, Function.identity(), (a, b) -> a));
  }
}
