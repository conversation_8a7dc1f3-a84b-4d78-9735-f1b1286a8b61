package com.solum.xplain.core.curvemarket;

import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView;
import java.util.Map;

/**
 * Contains the state form and quotes for a curve configuration
 *
 * @param stateForm the state form
 * @param quotes the quotes
 */
public record CurveConfigMarketStateQuotes(
    CurveConfigMarketStateForm stateForm, Map<String, CalculationMarketValueFullView> quotes) {}
