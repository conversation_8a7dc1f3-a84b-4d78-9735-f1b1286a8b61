package com.solum.xplain.core.classifiers.csv;

import static com.solum.xplain.core.common.csv.CsvColumn.textObject;

import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.index.RateIndex;
import com.opengamma.strata.collect.named.Named;
import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations;
import com.solum.xplain.core.curvegroup.conventions.index.IndexCurveConvention;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.extensions.index.OffshoreIndices;
import io.atlassian.fugue.Either;
import java.util.Comparator;
import java.util.List;
import java.util.function.Predicate;
import org.springframework.core.io.ByteArrayResource;

public class IborIndexConventionMapper extends CsvMapper<IborIndex> {

  private static final List<CsvColumn<IborIndex>> COLUMNS =
      List.of(
          textObject("name", IborIndex::getName),
          textObject("currency", FloatingRateIndex::getCurrency),
          textObject("tenor", RateIndex::getTenor),
          textObject("dayCount", FloatingRateIndex::getDayCount),
          textObject("defaultFixLegDayCount", FloatingRateIndex::getDefaultFixedLegDayCount),
          textObject("fixingCalendar", RateIndex::getFixingCalendar),
          textObject("effectiveDateOffset", IborIndex::getEffectiveDateOffset),
          textObject("fixingDateOffset", IborIndex::getFixingDateOffset),
          textObject("maturityDateOffset", IborIndex::getMaturityDateOffset));

  private IborIndexConventionMapper() {
    super(COLUMNS, null);
  }

  public static ByteArrayResource iborIndexConventionsCsv() {
    var mapper = new IborIndexConventionMapper();
    var rowsStream =
        ConventionalCurveConfigurations.ALL_CONVENTIONAL_CURVES.stream()
            .filter(IndexCurveConvention.class::isInstance)
            .map(IndexCurveConvention.class::cast)
            .map(IndexCurveConvention::getIndex)
            .filter(IborIndex.class::isInstance)
            .map(IborIndex.class::cast)
            .distinct()
            .sorted(Comparator.comparing(Named::getName))
            .filter(Predicate.not(OffshoreIndices::isOffshoreIborIndex))
            .map(mapper::toCsvRow)
            .map(Either::<ErrorItem, CsvRow>right);
    return new CsvOutputFile(mapper.header(), rowsStream).writeToByteArray();
  }
}
