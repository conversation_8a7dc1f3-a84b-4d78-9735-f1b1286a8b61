package com.solum.xplain.core.rules;

import java.util.Collection;
import java.util.Optional;

/** Facade for creating and managing rules and rule sets. */
public interface RulesService<R, S> {
  /**
   * Creates a rule set from a collection of rules. If the collection is ordered then the order of
   * the rules in the rule set is the same as the order of the rules in the collection (actually the
   * collection's spliterator). Otherwise the order of the rules is the natural order.
   *
   * @param rules the rules to create the rule set from
   * @return the rule set, ready to execute
   */
  S createRuleSet(Collection<R> rules);

  /**
   * Executes a rule set against a set of initial facts.
   *
   * @param ruleSet the rule set to execute
   * @param initialFacts object containing all the initial facts to execute the rules against - each
   *     property becomes a named fact
   * @param resultType the type of the result to return
   * @param <T> the type of the result to return
   * @return the result of executing the rule set, if a result was produced by the rules
   */
  <T> Optional<T> execute(S ruleSet, Object initialFacts, Class<T> resultType);

  /**
   * Creates a composite rule where only the first matching rule in the collection is executed. If
   * the collection is ordered then the order to check the rules is the same as the order of the
   * rules in the collection (actually the collection's spliterator). Otherwise the order of the
   * rules is the natural order.
   *
   * @param rules the rules to execute at most one of
   * @return a composite rule
   */
  R oneOf(Collection<R> rules);

  /**
   * Creates a composite rule where a predicate rule must match in order for the remaining rules to
   * be checked for a match. If the collection is ordered then the order to check the rules is the
   * same as the order of the rules in the collection (actually the collection's spliterator).
   * Otherwise the order of the rules is the natural order. The predicate rule will also be executed
   * (first) if it matches.
   *
   * @param predicate the rule to execute as a predicate
   * @param rules the rules to then check and execute if the predicate matches
   * @param priority the priority of the composite rule
   * @return a composite rule
   */
  R predicated(R predicate, Collection<R> rules, int priority);
}
