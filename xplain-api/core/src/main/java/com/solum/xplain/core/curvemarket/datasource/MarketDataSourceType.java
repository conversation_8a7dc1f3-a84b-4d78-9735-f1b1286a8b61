package com.solum.xplain.core.curvemarket.datasource;

import static com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueMappers.toMarketValueFullViewPrimary;
import static com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueMappers.toMarketValueFullViewSecondary;
import static com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueMappers.toMarketValueViewPrimary;
import static com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueMappers.toMarketValueViewSecondary;

import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueMapperFunction;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueView;
import java.util.Optional;
import java.util.function.Function;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum MarketDataSourceType {
  RAW_PRIMARY(
      "Raw Primary",
      toMarketValueViewPrimary(),
      toMarketValueFullViewPrimary(),
      MarketDataProviders::primaryProvider,
      false),
  RAW_SECONDARY(
      "Raw Secondary",
      toMarketValueViewSecondary(),
      toMarketValueFullViewSecondary(),
      MarketDataProviders::secondaryProvider,
      false),
  PRELIMINARY_PRIMARY(
      "Preliminary Primary",
      toMarketValueViewPrimary(),
      toMarketValueFullViewPrimary(),
      MarketDataProviders::primaryProvider,
      true),
  PRELIMINARY_SECONDARY(
      "Preliminary Secondary",
      toMarketValueViewSecondary(),
      toMarketValueFullViewSecondary(),
      MarketDataProviders::secondaryProvider,
      true),
  OVERLAY(
      "Overlay",
      toMarketValueViewPrimary(),
      toMarketValueFullViewPrimary(),
      MarketDataProviders::primaryProvider,
      true);

  private final String name;
  private final CalculationMarketValueMapperFunction<CalculationMarketValueView> toView;
  private final CalculationMarketValueMapperFunction<CalculationMarketValueFullView> toFullView;
  private final Function<MarketDataProviders, Optional<String>> providerExtractor;

  /**
   * If true, NaN values in calibration data will not be treated as an error during calibration.
   * This is useful for overlay data sources where missing data does not necessarily have to be
   * replaced with a default value.
   */
  private final boolean allowCalibrationMissingValues;

  public CalculationMarketValueMapperFunction<CalculationMarketValueView> getToView() {
    return toView;
  }

  public CalculationMarketValueMapperFunction<CalculationMarketValueFullView> getToFullView() {
    return toFullView;
  }

  public String getName() {
    return name;
  }

  public boolean allowCalibrationMissingValues() {
    return allowCalibrationMissingValues;
  }

  public Optional<String> resolveProvider(MarketDataProviders p) {
    return providerExtractor.apply(p);
  }
}
