package com.solum.xplain.core.classifiers;

import static com.solum.xplain.core.classifiers.csv.FixedIborSwapConventionMapper.fixedIborSwapConventionsCsv;
import static com.solum.xplain.core.classifiers.csv.FixedInflationSwapConventionMapper.fixedInflationSwapConventions;
import static com.solum.xplain.core.classifiers.csv.FixedOvernightSwapConventionMapper.fixedOvernightSwapConventionsCsv;
import static com.solum.xplain.core.classifiers.csv.ForwardRateAgreementConventionCsvMapper.fraConventionsCsv;
import static com.solum.xplain.core.classifiers.csv.FxSwapConventionMapper.fxSwapConventionsCsv;
import static com.solum.xplain.core.classifiers.csv.IborFutureContractSpecMapper.iborFutureContractsCsv;
import static com.solum.xplain.core.classifiers.csv.IborIborSwapConventionMapper.iborIborSwapConventionsCsv;
import static com.solum.xplain.core.classifiers.csv.IborIndexConventionMapper.iborIndexConventionsCsv;
import static com.solum.xplain.core.classifiers.csv.IborOvernightConventionMapper.iborOvernightConvention;
import static com.solum.xplain.core.classifiers.csv.ImmForwardRateAgreementConventionCsvMapper.immFraConventionsCsv;
import static com.solum.xplain.core.classifiers.csv.OvernightIndexConventionMapper.overnightIndexConventionsCsv;
import static com.solum.xplain.core.classifiers.csv.XCcyFixedOvernightSwapConventionMapper.xccyFixedOvernightSwapConventionsCsv;
import static com.solum.xplain.core.classifiers.csv.XCcyIborIborSwapConventionMapper.xccyIborIborSwapConventionsCsv;
import static com.solum.xplain.core.classifiers.csv.XCcyIborOvernightSwapConventionMapper.xccyIborOvernightSwapConventionsCsv;
import static com.solum.xplain.core.classifiers.csv.XCcyOvernightOvernightSwapConventionMapper.xccyOvernightOvernightSwapConventionsCsv;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static org.springframework.http.MediaType.APPLICATION_OCTET_STREAM_VALUE;

import com.solum.xplain.core.authentication.NoRoleAuthorization;
import com.solum.xplain.core.classifiers.conventions.CdsIndexConvention;
import com.solum.xplain.core.classifiers.conventions.IborIndexConvention;
import com.solum.xplain.core.classifiers.conventions.InflationIndexConvention;
import com.solum.xplain.core.classifiers.conventions.OvernightIndexConvention;
import com.solum.xplain.core.classifiers.conventions.OvernightTermIndexConvention;
import com.solum.xplain.core.classifiers.conventions.SwapConvention;
import com.solum.xplain.core.classifiers.conventions.SwapTradeConventions;
import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.csv.FilesResponseBuilder;
import io.swagger.v3.oas.annotations.Operation;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/** Created by danielius on 14/06/17. */
@RestController
@RequestMapping("/classifiers")
public class ClassifiersController {
  private final ClassifiersControllerService classifierControllerService;

  public ClassifiersController(ClassifiersControllerService classifierControllerService) {
    this.classifierControllerService = classifierControllerService;
  }

  @GetMapping
  @NoRoleAuthorization
  public List<Classifier> get() {
    return classifierControllerService.getClassifiers();
  }

  @Operation(summary = "Export conventions")
  @GetMapping(value = "/conventions/csv", produces = APPLICATION_OCTET_STREAM_VALUE)
  @CommonErrors
  @NoRoleAuthorization
  public ResponseEntity<ByteArrayResource> getStaticConfig() {
    return eitherErrorItemFileResponse(
        FilesResponseBuilder.newResponse("conventions.zip")
            .addFile("FixedIborSwap.csv", fixedIborSwapConventionsCsv())
            .addFile("FixedOvernightSwap.csv", fixedOvernightSwapConventionsCsv())
            .addFile("FXSwap.csv", fxSwapConventionsCsv())
            .addFile("IborIndex.csv", iborIndexConventionsCsv())
            .addFile("IborFuture.csv", iborFutureContractsCsv())
            .addFile("IborIborSwap.csv", iborIborSwapConventionsCsv())
            .addFile("IborOvernightSwap.csv", iborOvernightConvention())
            .addFile("OvernightIndex.csv", overnightIndexConventionsCsv())
            .addFile("XccyIborIborSwap.csv", xccyIborIborSwapConventionsCsv())
            .addFile("XccyOvernightOvernightSwap.csv", xccyOvernightOvernightSwapConventionsCsv())
            .addFile("FixedInflationSwap.csv", fixedInflationSwapConventions())
            .addFile("XccyIborOvernightSwap.csv", xccyIborOvernightSwapConventionsCsv())
            .addFile("XccyFixedOvernightSwap.csv", xccyFixedOvernightSwapConventionsCsv())
            .addFile("ForwardRateAgreement.csv", fraConventionsCsv())
            .addFile("ImmForwardRateAgreement.csv", immFraConventionsCsv())
            .build());
  }

  @Operation(summary = "Get swap conventions")
  @GetMapping(value = "/conventions")
  @CommonErrors
  @NoRoleAuthorization
  public List<SwapConvention> getConventions() {
    return SwapTradeConventions.CONVENTIONS;
  }

  @Operation(summary = "Get ibor index conventions")
  @GetMapping(value = "/conventions/ibor")
  @CommonErrors
  @NoRoleAuthorization
  public List<IborIndexConvention> getIborIndexClassifier() {
    return PermissibleConventions.IBOR_INDEX_CONVENTIONS;
  }

  @Operation(summary = "Get overnight index conventions")
  @GetMapping(value = "/conventions/overnight")
  @CommonErrors
  @NoRoleAuthorization
  public List<OvernightIndexConvention> getOvernightIndexClassifiers() {
    return PermissibleConventions.OVERNIGHT_INDEX_CONVENTIONS;
  }

  @Operation(summary = "Get inflation index conventions")
  @GetMapping(value = "/conventions/inflation")
  @CommonErrors
  @NoRoleAuthorization
  public List<InflationIndexConvention> getInflationIndexClassifier() {
    return PermissibleConventions.FIXED_INFLATION_CONVENTION_BY_INDEX.values().stream()
        .map(InflationIndexConvention::newOf)
        .toList();
  }

  @Operation(summary = "Get overnight term index conventions")
  @GetMapping(value = "/conventions/overnight-term")
  @CommonErrors
  @NoRoleAuthorization
  public List<OvernightTermIndexConvention> getOvernightTermIndexClassifier() {
    return PermissibleConventions.OVERNIGHT_TERM_INDEX_CONVENTIONS;
  }

  @Operation(summary = "Get credit index conventions")
  @GetMapping(value = "/conventions/credit-index")
  @CommonErrors
  @NoRoleAuthorization
  public Map<String, CdsIndexConvention> getCdsIndexConventions() {
    return Arrays.stream(CdsIndex.values())
        .map(CdsIndexConvention::newOf)
        .collect(Collectors.toMap(CdsIndexConvention::getName, Function.identity()));
  }
}
