package com.solum.xplain.core.viewconfig;

import com.solum.xplain.core.viewconfig.entity.ViewConfiguration;
import com.solum.xplain.core.viewconfig.value.View;
import java.util.List;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

public interface ViewConfigurationRepository extends MongoRepository<ViewConfiguration, ObjectId> {

  @Query("{'scope': ?0, $or: [{'createdBy.userId': ?1}, {'shared': true}]}")
  List<ViewConfiguration> findAllUserVisibleViews(View<?> scope, String userId);

  boolean existsByCreatedByUserIdAndScopeAndNameIgnoreCase(
      String userId, View<?> scope, String name);
}
