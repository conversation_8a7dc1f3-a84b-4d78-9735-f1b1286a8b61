package com.solum.xplain.core.companydocs.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.web.multipart.MultipartFile;

public class ValidDocumentSizeValidator
    implements ConstraintValidator<ValidDocumentSize, MultipartFile> {

  private static final int MAX_FILE_SIZE = 104_857_601; // 100MB + 1 Byte

  @Override
  public boolean isValid(MultipartFile value, ConstraintValidatorContext context) {
    return value == null || value.getSize() < MAX_FILE_SIZE;
  }
}
