package com.solum.xplain.core.curvemarket.pricetype;

import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class InstrumentPriceTypeResolver {

  private final InstrumentPriceRequirements priceRequirements;
  private final List<String> discountCurves;

  public static InstrumentPriceTypeResolver priceTypeResolver(
      InstrumentPriceRequirements priceRequirements) {
    return new InstrumentPriceTypeResolver(priceRequirements, List.of());
  }

  public static InstrumentPriceTypeResolver priceTypeResolver(
      InstrumentPriceRequirements priceRequirements, List<String> discountCurves) {
    return new InstrumentPriceTypeResolver(priceRequirements, discountCurves);
  }

  public InstrumentPriceType resolvePriceType(InstrumentDefinition instrument) {
    var priceGroup = instrument.getInstrument().getPriceGroup();
    var assetName = instrument.getAssetName();
    return switch (priceGroup) {
      case CURVE_PRICE ->
          isNotEmpty(assetName) && discountCurves.contains(assetName)
              ? priceRequirements.getDscCurvesPriceType()
              : priceRequirements.getCurvesPriceType();
      case FX_RATE_PRICE -> priceRequirements.getFxRatesPriceType();
      case VOL_PRICE -> priceRequirements.getVolsPriceType();
      case VOL_SKEW_PRICE -> priceRequirements.getVolsSkewsPriceType();
    };
  }
}
