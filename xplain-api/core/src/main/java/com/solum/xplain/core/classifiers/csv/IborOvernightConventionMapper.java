package com.solum.xplain.core.classifiers.csv;

import static com.solum.xplain.core.common.csv.CsvColumn.textObject;

import com.opengamma.strata.product.swap.type.OvernightIborSwapConvention;
import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import java.util.List;
import org.springframework.core.io.ByteArrayResource;

public class IborOvernightConventionMapper extends CsvMapper<OvernightIborSwapConvention> {

  private static final List<CsvColumn<OvernightIborSwapConvention>> COLUMNS =
      List.of(
          textObject("name", OvernightIborSwapConvention::getName),
          textObject("spotDateOffset", OvernightIborSwapConvention::getSpotDateOffset),
          textObject("iborLeg.index", x -> x.getIborLeg().getIndex()),
          textObject("iborLeg.currency", x -> x.getIborLeg().getCurrency()),
          textObject("iborLeg.accrualFreq", x -> x.getIborLeg().getAccrualFrequency()),
          textObject("iborLeg.dayCount", x -> x.getIborLeg().getDayCount()),
          textObject("iborLeg.accrualBDA", x -> x.getIborLeg().getAccrualBusinessDayAdjustment()),
          textObject("iborLeg.compounding", x -> x.getIborLeg().getCompoundingMethod()),
          textObject("iborLeg.fixingDateOffset", x -> x.getIborLeg().getFixingDateOffset()),
          textObject("iborLeg.fixingRelativeTo", x -> x.getIborLeg().getFixingRelativeTo()),
          textObject("iborLeg.rollConv", x -> x.getIborLeg().getRollConvention()),
          textObject("iborLeg.paymentDateOffset", x -> x.getIborLeg().getPaymentDateOffset()),
          textObject("iborLeg.payFreq", x -> x.getIborLeg().getPaymentFrequency()),
          textObject("iborLeg.stubConv", x -> x.getIborLeg().getStubConvention()),
          textObject("iborLeg.startBDA", x -> x.getIborLeg().getStartDateBusinessDayAdjustment()),
          textObject("iborLeg.endBDA", x -> x.getIborLeg().getEndDateBusinessDayAdjustment()),
          textObject("overnightLeg.index", x -> x.getOvernightLeg().getIndex()),
          textObject("overnightLeg.currency", x -> x.getOvernightLeg().getCurrency()),
          textObject("overnightLeg.accrualFreq", x -> x.getOvernightLeg().getAccrualFrequency()),
          textObject("overnightLeg.accrual", x -> x.getOvernightLeg().getAccrualMethod()),
          textObject("overnightLeg.dayCount", x -> x.getOvernightLeg().getDayCount()),
          textObject(
              "overnightLeg.accrualBDA",
              x -> x.getOvernightLeg().getAccrualBusinessDayAdjustment()),
          textObject("overnightLeg.compounding", x -> x.getOvernightLeg().getCompoundingMethod()),
          textObject("overnightLeg.rateCutOffDays", x -> x.getOvernightLeg().getRateCutOffDays()),
          textObject("overnightLeg.rollConv", x -> x.getOvernightLeg().getRollConvention()),
          textObject(
              "overnightLeg.paymentDateOffset", x -> x.getOvernightLeg().getPaymentDateOffset()),
          textObject("overnightLeg.payFreq", x -> x.getOvernightLeg().getPaymentFrequency()),
          textObject("overnightLeg.stubConv", x -> x.getOvernightLeg().getStubConvention()),
          textObject(
              "overnightLeg.startBDA",
              x -> x.getOvernightLeg().getStartDateBusinessDayAdjustment()),
          textObject(
              "overnightLeg.endBDA", x -> x.getOvernightLeg().getEndDateBusinessDayAdjustment()));

  private IborOvernightConventionMapper() {
    super(COLUMNS, null);
  }

  public static ByteArrayResource iborOvernightConvention() {
    return new ConventionalTradeConventionExporter<>(
            new IborOvernightConventionMapper(), OvernightIborSwapConvention.class)
        .export();
  }
}
