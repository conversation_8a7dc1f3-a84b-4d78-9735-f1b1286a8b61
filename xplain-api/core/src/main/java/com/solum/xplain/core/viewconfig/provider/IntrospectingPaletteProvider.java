package com.solum.xplain.core.viewconfig.provider;

import static com.solum.xplain.core.utils.ReflectionUtils.lombokFieldNameConstantsStream;
import static java.util.function.Predicate.not;
import static org.reflections.ReflectionUtils.Fields;
import static org.reflections.util.ReflectionUtilsPredicates.withAnnotation;
import static org.springframework.core.annotation.AnnotatedElementUtils.findMergedAnnotation;
import static org.springframework.core.annotation.AnnotatedElementUtils.findMergedRepeatableAnnotations;

import com.solum.xplain.core.classifiers.Classifier;
import com.solum.xplain.core.classifiers.ClassifiersAggregator;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewField;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewIgnore;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewMapping;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewSubfield;
import com.solum.xplain.core.viewconfig.filter.FilterClauseTranslator;
import com.solum.xplain.core.viewconfig.value.FieldDefinitionView;
import com.solum.xplain.core.viewconfig.value.FieldType;
import com.solum.xplain.core.viewconfig.value.PaletteView;
import com.solum.xplain.core.viewconfig.value.View;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.time.*;
import java.time.temporal.Temporal;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.reflections.ReflectionUtils;
import org.springframework.stereotype.Component;

/**
 * Defines palettes for all view classes annotated with {@link
 * com.solum.xplain.core.viewconfig.annotation.ConfigurableView @ConfigurableView}.
 */
@Component
@RequiredArgsConstructor
@Slf4j
@NullMarked
public class IntrospectingPaletteProvider implements PaletteProvider {
  private final List<ConfigurableViewProvider> configurableViewProviders;
  private final List<FilterClauseTranslator> filterClauseTranslators;
  private final ClassifiersAggregator classifiersAggregator;
  private @Nullable List<PaletteView<?>> palettes;

  @PostConstruct
  void buildPalettes() {
    var views = configurableViewProviders.stream().flatMap(it -> it.getViews().stream());
    //noinspection unchecked,rawtypes
    this.palettes = (List<PaletteView<?>>) (List) views.map(this::paletteForView).toList();
    log.info("Created {} configurable view palettes", palettes.size());
  }

  @Override
  public Collection<PaletteView<?>> providePalettes() {
    assert palettes != null; // This will be after the PostConstruct method has run
    return palettes;
  }

  private PaletteView<?> paletteForView(View<?> view) {
    log.debug("Creating configurable view palette for view {}", view.viewClass().getSimpleName());
    return new PaletteView<>(view, fieldsForClass(view.viewClass()).toList());
  }

  private Stream<FieldDefinitionView> fieldsForClass(Class<?> viewClass) {
    return fieldsForClass(viewClass, "", "", Collections.emptyMap());
  }

  /**
   * Recursive method to walk the field hierarchy of a view class and build a FieldDefinitionView
   * for each leaf field.
   *
   * <p>A leaf field is a number, boolean, enum, date or string, or any field annotated with
   * {@code @ConfigurableViewField(value = <non-empty>)} or inheriting a mapping. Note that a field
   * can result in more than one field definition, if annotations are used to map the field to
   * multiple names.
   *
   * <p>If a field is not a leaf field, and is not annotated with {@code @ConfigurableViewIgnore},
   * it is considered to be a nested object and this method is called recursively on its type. If
   * the non-leaf field is a collection type, then the recursion happens on the generic type of the
   * collection. If a {@code ConfigurableViewMapping} annotation is present on the field, it is used
   * to remap the names of (or ignore) fields of its nested type. This takes precedence over any
   * {@code ConfigurableViewField} annotations for those fields.
   */
  private Stream<FieldDefinitionView> fieldsForClass(
      Class<?> viewClass,
      String fieldPrefix,
      String pathPrefix,
      Map<String, Set<ConfigurableViewMapping>> parentMappings) {
    //noinspection unchecked
    Field viewSubfield =
        ReflectionUtils.get(Fields.of(viewClass), withAnnotation(ConfigurableViewSubfield.class))
            .stream()
            .findFirst()
            .orElse(null);
    Set<Field> viewFields =
        ReflectionUtils.get(
            Fields.of(viewClass),
            not(
                withAnnotation(ConfigurableViewIgnore.class)
                    .or(withAnnotation(ConfigurableViewSubfield.class))));
    Set<String> lombokFieldNameConstants =
        lombokFieldNameConstantsStream(viewClass).map(Field::getName).collect(Collectors.toSet());

    return viewFields.stream()
        .filter(f -> shouldIncludeField(f, lombokFieldNameConstants))
        .flatMap(
            field -> {
              String fieldName = field.getName();
              Set<ConfigurableViewMapping> mappingAnns =
                  parentMappings.getOrDefault(fieldName, Collections.emptySet());
              Set<ConfigurableViewField> fieldAnns =
                  findMergedRepeatableAnnotations(field, ConfigurableViewField.class);
              FieldType fieldType = toFieldType(field.getType());

              if (isLeafField(fieldType, mappingAnns, fieldAnns)) {
                return handleLeafField(
                    fieldPrefix,
                    pathPrefix,
                    field,
                    fieldType,
                    viewSubfield,
                    mappingAnns,
                    fieldAnns);
              } else {
                return recurseFieldsForNestedClass(fieldPrefix, pathPrefix, field, fieldAnns);
              }
            });
  }

  /**
   * Calls {@link #fieldsForClass(Class, String, String, Map)} recursively for a non-leaf field.
   *
   * <p>If a field is not a leaf field, and is not annotated with {@code @ConfigurableViewIgnore},
   * it is considered to be a nested object and its fields are built into FieldDefinitionView
   * objects. If the non-leaf field is a collection type, then the recursion happens on the generic
   * type of the collection. If a {@code ConfigurableViewMapping} annotation is present on the
   * field, it is used to remap the names of (or ignore) fields of its nested type. This takes
   * precedence over any {@code ConfigurableViewField} annotations for those fields.
   *
   * <p>Note that a nested field can result in the class being scanned more than once, if
   * annotations are used to map the class with different prefixes.
   */
  private Stream<FieldDefinitionView> recurseFieldsForNestedClass(
      String fieldPrefix, String pathPrefix, Field field, Set<ConfigurableViewField> fieldAnns) {
    Class<?> nestedType = getNestedType(field);
    Map<String, Set<ConfigurableViewMapping>> nestedFieldMappings =
        groupByFieldName(findMergedRepeatableAnnotations(field, ConfigurableViewMapping.class));
    String nestedPathPrefix = pathPrefix + field.getName() + ".";
    if (fieldAnns.isEmpty()) {
      return fieldsForClass(nestedType, fieldPrefix, nestedPathPrefix, nestedFieldMappings);
    } else {
      return fieldAnns.stream()
          .flatMap(
              fieldAnn -> {
                String nestedFieldPrefix = fieldPrefix + fieldAnn.prefix();
                return fieldsForClass(
                    nestedType,
                    nestedFieldPrefix,
                    fieldAnn.unwrapPropertyPath() ? pathPrefix : nestedPathPrefix,
                    nestedFieldMappings);
              });
    }
  }

  /**
   * Build FieldDefinitionViews for a leaf field.
   *
   * <p>A leaf field is a number, boolean, enum, date or string, or any field annotated with
   * {@code @ConfigurableViewField(value = <non-empty>)} or inheriting a mapping. Note that a field
   * can result in more than one field definition, if annotations are used to map the field to
   * multiple names, or if the field has subfields.
   */
  private Stream<FieldDefinitionView> handleLeafField(
      String fieldPrefix,
      String pathPrefix,
      Field field,
      @Nullable FieldType fieldType,
      @Nullable Field viewSubfield,
      Set<ConfigurableViewMapping> mappingAnns,
      Set<ConfigurableViewField> fieldAnns) {
    ConfigurableViewQuery queryAnn = findMergedAnnotation(field, ConfigurableViewQuery.class);
    FieldType effectiveType = fieldType == null ? FieldType.STRING : fieldType;
    final String enumClassifier =
        fieldType == FieldType.ENUM ? toEnumClassifier(field.getType()) : null;
    final String propertyPath = pathPrefix + field.getName();
    final String subfieldClassifier;
    final String subfieldPropertyPath;
    if (viewSubfield != null) {
      ConfigurableViewSubfield subfieldAnn =
          findMergedAnnotation(viewSubfield, ConfigurableViewSubfield.class);
      subfieldClassifier = toSubfieldClassifier(viewSubfield, subfieldAnn);
      subfieldPropertyPath = pathPrefix + viewSubfield.getName();
    } else {
      subfieldClassifier = subfieldPropertyPath = null;
    }

    var filterClauseTranslator =
        queryAnn != null && queryAnn.filterClauseTranslator() != FilterClauseTranslator.class
            ? getFilterClauseTranslator(queryAnn.filterClauseTranslator())
            : null;

    return getFieldDefinitionViews(
        fieldPrefix,
        field.getName(),
        effectiveType,
        propertyPath,
        enumClassifier,
        subfieldClassifier,
        subfieldPropertyPath,
        queryAnn != null && queryAnn.sortable(),
        filterClauseTranslator,
        mappingAnns,
        fieldAnns);
  }

  /**
   * Look up the filter clause translator for this type. Uses the list of filter clause translators
   * injected as beans to find the one that matches the type.
   *
   * @param type the class of the translator
   * @return the translator, or null if no translator is found for the type
   */
  private @Nullable FilterClauseTranslator getFilterClauseTranslator(
      Class<? extends FilterClauseTranslator> type) {
    return filterClauseTranslators.stream().filter(type::isInstance).findFirst().orElse(null);
  }

  /**
   * Skip synthetic fields (in part so that things don't die with a Groovy class in tests), but also
   * we know we only want real stuff. If we've got @FieldNameConstants, then skip anything that
   * doesn't appear in them.
   */
  private boolean shouldIncludeField(Field field, Set<String> lombokFieldNameConstants) {
    if (field.isSynthetic()) {
      log.info(
          "Skipping synthetic field {} in view class {}",
          field.getName(),
          field.getDeclaringClass().getName());
      return false;
    }
    if (!lombokFieldNameConstants.isEmpty()
        && !lombokFieldNameConstants.contains(field.getName())) {
      log.info(
          "Skipping field {} in view class {} because it is not a field declared by @FieldNameConstants",
          field.getName(),
          field.getDeclaringClass().getName());
      return false;
    }
    return true;
  }

  /**
   * A field is a leaf field if it has a non-null FieldType, or if it has a non-empty value in a
   * ConfigurableViewField or ConfigurableViewMapping annotations.
   *
   * @param fieldType the type of the field as determined by {@link #toFieldType(Class)}
   * @param mappingAnns the mapping annotations applied to this field name in the parent (or empty)
   * @param fieldAnns the {@link ConfigurableViewField} annotations on the field (can be empty)
   * @return true if this field is a leaf field i.e. should result in one or more
   *     FieldDefinitionView instances
   */
  private boolean isLeafField(
      @Nullable FieldType fieldType,
      Set<ConfigurableViewMapping> mappingAnns,
      Set<ConfigurableViewField> fieldAnns) {
    return fieldType != null
        || mappingAnns.stream().anyMatch(mappingAnn -> !mappingAnn.ignore())
        || fieldAnns.stream().anyMatch(fieldAnn -> !fieldAnn.value().isEmpty());
  }

  /**
   * Look up the classifier associated with this field's type.
   *
   * @param type the class of the field
   * @return the classifier id, or null if no classifier is found for the type
   */
  private @Nullable String toEnumClassifier(Class<?> type) {
    Classifier classifier = classifiersAggregator.getClassifierBySourceType(type);
    if (classifier != null) {
      return classifier.getId();
    }
    return null;
  }

  private String toSubfieldClassifier(Field field, @Nullable ConfigurableViewSubfield subfieldAnn) {
    String inferredClassifier =
        toFieldType(field.getType()) == FieldType.ENUM ? toEnumClassifier(field.getType()) : null;
    return StringUtils.defaultIfEmpty(
        subfieldAnn == null ? null : subfieldAnn.classifier(), inferredClassifier);
  }

  /**
   * Get field definition views for a leaf field. If the field has no mapping or field annotations,
   * a single FieldDefinitionView is returned. If the field has mapping annotations from its parent,
   * or field annotations, a FieldDefinitionView is returned for each annotation that isn't marked
   * as ignored.
   *
   * @param fieldPrefix the prefix to use for the field name, inherited from the parent (or empty)
   * @param fieldName the field name
   * @param effectiveType the FieldType of the field
   * @param propertyPath the dot-separated path to the field from the root of the view
   * @param enumClassifier the enum classifier inferred from the field type, or null if not an enum
   *     or nothing to infer
   * @param subfieldClassifier the subfield classifier for field inferred from or explicit on the
   *     {@code @ConfigurableViewSubfield} field type, or null if not part of a subfield
   * @param sortable true if the field is sortable (taken from {@code @ConfigurableViewQuery}
   *     annotation).
   * @param filterClauseTranslator optional bean class to use for translating filter clauses for
   *     this field
   * @param mappingAnns the mapping annotations applied to this field name in the parent (or empty)
   * @param fieldAnns the field annotations applied to this field (or empty)
   * @return a stream of field definitions for this field (more than one if more than one mapping or
   *     field annotation applies)
   */
  private Stream<FieldDefinitionView> getFieldDefinitionViews(
      String fieldPrefix,
      String fieldName,
      FieldType effectiveType,
      String propertyPath,
      @Nullable String enumClassifier,
      @Nullable String subfieldClassifier,
      @Nullable String subfieldPropertyPath,
      boolean sortable,
      @Nullable FilterClauseTranslator filterClauseTranslator,
      Set<ConfigurableViewMapping> mappingAnns,
      Set<ConfigurableViewField> fieldAnns) {
    if (mappingAnns.isEmpty() && fieldAnns.isEmpty()) {
      return Stream.of(
          new FieldDefinitionView(
              fieldPrefix + fieldName,
              effectiveType,
              propertyPath,
              enumClassifier,
              sortable,
              subfieldClassifier,
              subfieldPropertyPath,
              null,
              filterClauseTranslator));
    } else if (!mappingAnns.isEmpty()) {
      // Mapping annotations take priority over field annotations
      return mappingAnns.stream()
          .filter(mappingAnn -> !mappingAnn.ignore())
          .map(
              mappingAnn -> {
                String effectiveName = StringUtils.defaultIfEmpty(mappingAnn.to(), fieldName);
                return new FieldDefinitionView(
                    fieldPrefix + effectiveName,
                    effectiveType,
                    propertyPath,
                    enumClassifier,
                    sortable,
                    subfieldClassifier,
                    subfieldPropertyPath,
                    mappingAnn.deriveAs(),
                    filterClauseTranslator);
              });
    } else {
      return fieldAnns.stream()
          .map(
              fieldAnn -> {
                String effectiveName = StringUtils.defaultIfEmpty(fieldAnn.value(), fieldName);
                String effectiveEnumClassifier =
                    StringUtils.defaultIfEmpty(fieldAnn.enumClassifier(), enumClassifier);
                return new FieldDefinitionView(
                    fieldPrefix + effectiveName,
                    effectiveEnumClassifier != null ? FieldType.ENUM : effectiveType,
                    propertyPath,
                    effectiveEnumClassifier,
                    sortable,
                    subfieldClassifier,
                    subfieldPropertyPath,
                    fieldAnn.deriveAs(),
                    filterClauseTranslator);
              });
    }
  }

  /**
   * Determine the effective class of the field to use when recursing. If the field is a collection,
   * the generic element type is used, otherwise it is the raw type of the field.
   *
   * @param field the field to get the type of
   * @return the effective class of the field
   */
  private Class<?> getNestedType(Field field) {
    return (Collection.class.isAssignableFrom(field.getType())
            && field.getGenericType() instanceof ParameterizedType)
        ? (Class<?>) ((ParameterizedType) field.getGenericType()).getActualTypeArguments()[0]
        : (Map.class.isAssignableFrom(field.getType())
                && field.getGenericType() instanceof ParameterizedType)
            ? (Class<?>) ((ParameterizedType) field.getGenericType()).getActualTypeArguments()[1]
            : field.getType();
  }

  /**
   * Group the set of mappings using the field name ({@link ConfigurableViewMapping#from()} as the
   * key.
   *
   * @param mappings the set of all mappings from the parent field
   * @return a map of field name to set of mappings
   */
  private HashMap<String, Set<ConfigurableViewMapping>> groupByFieldName(
      Set<ConfigurableViewMapping> mappings) {
    return mappings.stream()
        .reduce(
            new HashMap<>(),
            (result, mappingAnn) -> {
              result.computeIfAbsent(mappingAnn.from(), k -> new HashSet<>()).add(mappingAnn);
              return result;
            },
            (a, b) -> {
              a.putAll(b);
              return a;
            });
  }

  /**
   * Determine the FieldType of a field based on its class.
   *
   * @param fieldType the class of the field
   * @return the FieldType of the field, or null if it does not map to one of the known value types
   */
  private @Nullable FieldType toFieldType(Class<?> fieldType) {
    if (isNumeric(fieldType)) {
      return FieldType.NUMBER;
    } else if (isBoolean(fieldType)) {
      return FieldType.BOOLEAN;
    } else if (isEnum(fieldType)) {
      return FieldType.ENUM;
    } else if (isTime(fieldType)) {
      return FieldType.TIME;
    } else if (isDateTime(fieldType)) {
      return FieldType.DATETIME;
    } else if (isDate(fieldType)) {
      return FieldType.DATE;
    } else if (String.class == fieldType) {
      return FieldType.STRING;
    }
    return null;
  }

  /**
   * A numeric class is one that is a subclass of Number, or is a primitive number type.
   *
   * @param fieldType the class to check
   * @return true if the class is a numeric type
   */
  private boolean isNumeric(Class<?> fieldType) {
    return Number.class.isAssignableFrom(fieldType)
        || int.class == fieldType
        || long.class == fieldType
        || float.class == fieldType
        || double.class == fieldType
        || short.class == fieldType;
  }

  private boolean isBoolean(Class<?> fieldType) {
    return Boolean.class == fieldType || boolean.class == fieldType;
  }

  /**
   * An enum class is one that is a subclass of Enum, or is an interface annotated with @Schema(type
   * = "enum") or @Schema(anyOf = {(enum class), ...}).
   *
   * @param fieldType the class to check
   * @return true if the class is an enum type
   */
  private boolean isEnum(Class<?> fieldType) {
    return Enum.class.isAssignableFrom(fieldType)
        || (fieldType.isInterface()
            && fieldType.isAnnotationPresent(Schema.class)
            && isEnumSchema(fieldType.getAnnotation(Schema.class)));
  }

  /**
   * An enum schema is one that is annotated with @Schema(type = "enum") or @Schema(anyOf = {(enum
   * class), ...}).
   *
   * @param annotation the schema annotation to check
   * @return true if the schema is an enum type
   */
  private boolean isEnumSchema(Schema annotation) {
    return "enum".equals(annotation.type())
        || (annotation.anyOf().length > 0 && Enum.class.isAssignableFrom(annotation.anyOf()[0]));
  }

  /**
   * A datetime class is one that is a subclass of LocalDateTime, ZonedDateTime, or OffsetDateTime.
   *
   * @return true if a class is of datetime type
   */
  private boolean isDateTime(Class<?> fieldType) {
    return LocalDateTime.class.isAssignableFrom(fieldType)
        || ZonedDateTime.class.isAssignableFrom(fieldType)
        || OffsetDateTime.class.isAssignableFrom(fieldType);
  }

  /**
   * A time class is one that is a subclass of LocalTime.
   *
   * @param fieldType the class to check
   * @return true if the class is a time type
   */
  private boolean isTime(Class<?> fieldType) {
    return LocalTime.class.isAssignableFrom(fieldType);
  }

  /**
   * A date class is one that is a subclass of {@link Temporal}.
   *
   * @param fieldType the class to check
   * @return true if the class is a date type
   */
  private boolean isDate(Class<?> fieldType) {
    return Temporal.class.isAssignableFrom(fieldType);
  }
}
