package com.solum.xplain.core.companydocs;

import static com.solum.xplain.core.files.FileOperations.FILES_COLLECTION;
import static com.solum.xplain.core.files.FileOperations.METADATA_FIELD;
import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.companydocs.entity.DocMetadata;
import com.solum.xplain.core.companydocs.value.CompanyDocForm;
import com.solum.xplain.core.companydocs.value.CompanyDocView;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.files.FileOperations;
import com.solum.xplain.core.users.AuditUser;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.data.auditing.AuditingHandler;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.gridfs.GridFsOperations;
import org.springframework.stereotype.Repository;

@Slf4j
@Repository
public class CompanyDocsRepository {

  private final MongoOperations mongoTemplate;
  private final AuditingHandler auditingHandler;
  private final FileOperations<DocMetadata> fileOperations;

  public CompanyDocsRepository(
      MongoOperations mongoTemplate,
      AuditingHandler auditingHandler,
      GridFsOperations gridFsOperations) {
    this.mongoTemplate = mongoTemplate;
    this.auditingHandler = auditingHandler;
    this.fileOperations = new FileOperations<>(gridFsOperations);
  }

  public Either<ErrorItem, EntityId> saveDocument(String companyId, CompanyDocForm form) {
    var metadata = metadata(companyId, form);
    var file = form.getFile();

    return fileOperations.saveFile(file, metadata);
  }

  public List<CompanyDocView> companyDocuments(String companyId) {
    var operations =
        List.of(
            Aggregation.match(nonArchivedCompanyDocumentsCriteria(companyId)),
            viewProjection(),
            Aggregation.sort(Sort.by(CompanyDocView.Fields.filename)));

    return mongoTemplate
        .aggregateAndReturn(CompanyDocView.class)
        .inCollection(FILES_COLLECTION)
        .by(Aggregation.newAggregation(operations))
        .all()
        .getMappedResults();
  }

  public Either<ErrorItem, FileResponseEntity> exportDocument(String companyId, String fileId) {
    return fileOperations.exportFile(query(nonArchivedDocumentIdCriteria(companyId, fileId)));
  }

  public Either<ErrorItem, EntityId> archiveDocument(String companyId, String fileId) {
    var criteria = nonArchivedDocumentIdCriteria(companyId, fileId);
    var update =
        Update.update(
            joinPaths(METADATA_FIELD, DocMetadata.Fields.archivedAt), LocalDateTime.now());

    var result = mongoTemplate.updateFirst(query(criteria), update, FILES_COLLECTION);

    return Eithers.cond(
        result.getModifiedCount() == 1,
        Error.OBJECT_NOT_FOUND.entity("Document not found"),
        EntityId.entityId(fileId));
  }

  private DocMetadata metadata(String companyId, CompanyDocForm form) {
    var file = form.getFile();
    var metadata = new DocMetadata();

    metadata.setCompanyId(companyId);
    metadata.setFilename(file.getOriginalFilename());
    metadata.setDescription(form.getDescription());
    metadata.setContentType(file.getContentType());
    metadata.setFileSize(file.getSize());
    return auditingHandler.markCreated(metadata);
  }

  private Criteria nonArchivedDocumentIdCriteria(String companyId, String fileId) {
    return Criteria.where(UNDERSCORE_ID)
        .is(new ObjectId(fileId))
        .andOperator(nonArchivedCompanyDocumentsCriteria(companyId));
  }

  private Criteria nonArchivedCompanyDocumentsCriteria(String companyId) {
    return Criteria.where(joinPaths(METADATA_FIELD, DocMetadata.Fields.companyId))
        .is(companyId)
        .and(joinPaths(METADATA_FIELD, DocMetadata.Fields.archivedAt))
        .isNull();
  }

  private ProjectionOperation viewProjection() {
    return project()
        .and(joinPaths(METADATA_FIELD, DocMetadata.Fields.companyId))
        .as(CompanyDocView.Fields.companyId)
        .and(joinPaths(METADATA_FIELD, DocMetadata.Fields.filename))
        .as(CompanyDocView.Fields.filename)
        .and(joinPaths(METADATA_FIELD, DocMetadata.Fields.description))
        .as(CompanyDocView.Fields.description)
        .and(joinPaths(METADATA_FIELD, DocMetadata.Fields.contentType))
        .as(CompanyDocView.Fields.contentType)
        .and(joinPaths(METADATA_FIELD, DocMetadata.Fields.fileSize))
        .as(CompanyDocView.Fields.fileSize)
        .and(joinPaths(METADATA_FIELD, DocMetadata.Fields.createdAt))
        .as(CompanyDocView.Fields.createdAt)
        .and(joinPaths(METADATA_FIELD, DocMetadata.Fields.createdBy, AuditUser.Fields.name))
        .as(CompanyDocView.Fields.creatorName)
        .and(joinPaths(METADATA_FIELD, DocMetadata.Fields.createdBy, AuditUser.Fields.userId))
        .as(CompanyDocView.Fields.creatorId);
  }
}
