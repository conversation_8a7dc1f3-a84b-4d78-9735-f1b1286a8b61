package com.solum.xplain.core.classifiers.csv;

import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.extensions.immfra.ImmutableImmFraConvention;
import java.util.List;
import org.springframework.core.io.ByteArrayResource;

public class ImmForwardRateAgreementConventionCsvMapper
    extends CsvMapper<ImmutableImmFraConvention> {

  private static final List<CsvColumn<ImmutableImmFraConvention>> COLUMNS =
      List.of(
          CsvColumn.textObject("name", ImmutableImmFraConvention::getName),
          CsvColumn.textObject("index", ImmutableImmFraConvention::getIndex),
          CsvColumn.textObject("currency", ImmutableImmFraConvention::getCurrency),
          CsvColumn.textObject("dayCount", ImmutableImmFraConvention::getDayCount),
          CsvColumn.textObject("dateSequence", ImmutableImmFraConvention::getDateSequence),
          CsvColumn.textObject(
              "businessDayAdjustment", ImmutableImmFraConvention::getBusinessDayAdjustment),
          CsvColumn.textObject("fixingDateOffset", ImmutableImmFraConvention::getFixingDateOffset),
          CsvColumn.textObject(
              "paymentDateOffset", ImmutableImmFraConvention::getPaymentDateOffset),
          CsvColumn.textObject("discountingMethod", ImmutableImmFraConvention::getDiscounting));

  protected ImmForwardRateAgreementConventionCsvMapper() {
    super(COLUMNS, null);
  }

  public static ByteArrayResource immFraConventionsCsv() {
    return new ConventionalTradeConventionExporter<>(
            new ImmForwardRateAgreementConventionCsvMapper(), ImmutableImmFraConvention.class)
        .export();
  }
}
