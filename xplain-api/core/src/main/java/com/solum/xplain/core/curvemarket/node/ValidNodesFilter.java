package com.solum.xplain.core.curvemarket.node;

import java.util.Collection;
import java.util.List;
import java.util.stream.Stream;

@FunctionalInterface
public interface ValidNodesFilter {
  ValidNodesFilter EMPTY_FILTER =
      new ValidNodesFilter() {
        @Override
        public <T> List<T> filterNodes(List<NodeInstrumentWrapper<T>> wrappedNodes) {
          return Stream.ofNullable(wrappedNodes)
              .flatMap(Collection::stream)
              .map(NodeInstrumentWrapper::getNode)
              .toList();
        }
      };

  <T> List<T> filterNodes(List<NodeInstrumentWrapper<T>> wrappedNodes);
}
