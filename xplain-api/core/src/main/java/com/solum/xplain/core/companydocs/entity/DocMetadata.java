package com.solum.xplain.core.companydocs.entity;

import com.solum.xplain.core.users.AuditUser;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;

@Data
@FieldNameConstants
public class DocMetadata {

  private String companyId;
  private String filename;
  private String description;
  private String contentType;
  private long fileSize;
  @CreatedBy private AuditUser createdBy;
  @CreatedDate private LocalDateTime createdAt;

  private LocalDateTime archivedAt;
}
