package com.solum.xplain.core.rules.jeasy;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.solum.xplain.core.rules.RulesService;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import java.util.Spliterator;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;
import lombok.RequiredArgsConstructor;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.api.Rules;
import org.jeasy.rules.core.DefaultRulesEngine;
import org.jeasy.rules.core.RuleBuilder;
import org.jeasy.rules.support.composite.ActivationRuleGroup;
import org.jeasy.rules.support.composite.ConditionalRuleGroup;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.PropertyAccessorFactory;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class EasyRulesService implements RulesService<Rule, Rules> {

  /**
   * The priority to use for the first rules in an ordered set of rules. Additional rules can be
   * inserted at lower priority.
   */
  private static final int FIRST_RULE_PRIORITY = 100;

  /** The highest priority, can be used to override the FIRST_RULE_PRIORITY. */
  private static final int HIGHEST_PRIORITY = 0;

  private final ObjectMapper objectMapper;

  @Override
  public Rules createRuleSet(Collection<Rule> rules) {
    Set<Rule> orderedRules = orderedRules(rules);
    return new Rules(orderedRules);
  }

  private Set<Rule> orderedRules(Collection<Rule> rules) {
    Spliterator<Rule> spliterator = rules.spliterator();
    if (spliterator.hasCharacteristics(Spliterator.ORDERED)) {
      // If it's not sorted, or has an explicit comparator, then we can't rely on natural ordering
      if (!spliterator.hasCharacteristics(Spliterator.SORTED)
          || spliterator.getComparator() != null) {
        AtomicInteger i = new AtomicInteger(FIRST_RULE_PRIORITY);
        return StreamSupport.stream(spliterator, false)
            .map(rule -> ruleWithPriority(rule, i.getAndIncrement()))
            .collect(Collectors.toSet());
      }
    }
    return new HashSet<>(rules);
  }

  Rule ruleWithPriority(Rule rule, int priority) {
    return new RuleBuilder()
        .name(rule.getName())
        .description(rule.getDescription())
        .priority(priority)
        .when(rule::evaluate)
        .then(rule::execute)
        .build();
  }

  @Override
  public Rule oneOf(Collection<Rule> rules) {
    ActivationRuleGroup activationRuleGroup = new ActivationRuleGroup();
    orderedRules(rules).forEach(activationRuleGroup::addRule);
    return activationRuleGroup;
  }

  @Override
  public Rule predicated(Rule predicate, Collection<Rule> rules, int priority) {
    // ConditionalRuleGroup treats the highest priority rule as the predicate, so we add it at
    // {@link #HIGHEST_PRIORITY}.
    ConditionalRuleGroup conditionalRuleGroup =
        new ConditionalRuleGroup(UUID.randomUUID().toString(), "", priority);
    orderedRules(rules).forEach(conditionalRuleGroup::addRule);
    conditionalRuleGroup.addRule(ruleWithPriority(predicate, HIGHEST_PRIORITY));
    return conditionalRuleGroup;
  }

  /**
   * {@inheritDoc}
   *
   * @return a fact matching the resultType, if one was produced by the rules. This must either have
   *     been in the initial facts or added to the facts by the rules.
   */
  @Override
  public <T> Optional<T> execute(Rules ruleSet, Object initialFacts, Class<T> resultType) {
    // Get a map containing all the properties from 'facts'
    Facts facts = new Facts();
    BeanWrapper initialFactsBeanWrapper =
        PropertyAccessorFactory.forBeanPropertyAccess(initialFacts);
    Arrays.stream(initialFactsBeanWrapper.getPropertyDescriptors())
        .forEach(
            pd -> facts.put(pd.getName(), initialFactsBeanWrapper.getPropertyValue(pd.getName())));

    synchronized (ruleSet) {
      new DefaultRulesEngine().fire(ruleSet, facts);
    }

    return StreamSupport.stream(facts.spliterator(), false)
        .filter(fact -> resultType.isInstance(fact.getValue()))
        .map(fact -> resultType.cast(fact.getValue()))
        .findFirst();
  }
}
