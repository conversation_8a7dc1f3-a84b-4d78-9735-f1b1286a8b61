package com.solum.xplain.core.company;

import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.entity.CompanyIpvSettings;
import com.solum.xplain.core.company.entity.CompanyLegalEntityIpvSettings;
import com.solum.xplain.core.company.mapper.IpvDataGroupRuleMapper;
import com.solum.xplain.core.company.mapper.IpvProvidersRuleMapper;
import com.solum.xplain.core.company.repository.CompanyIpvSettingsRepository;
import com.solum.xplain.core.company.repository.CompanyLegalEntityIpvSettingsRepository;
import com.solum.xplain.core.ipv.group.IpvDataGroupRepository;
import com.solum.xplain.core.ipv.tradeleveloverride.repository.CachingTradeLevelOverrideService;
import com.solum.xplain.core.rules.RulesService;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.api.Rules;
import org.springframework.stereotype.Service;

/**
 * Generates rulesets for determining valuation data group or providers for a trade. The rulesets
 * returned expect to be run against an {@link com.solum.xplain.core.company.mapper.IpvRuleFacts}
 * context and return a {@link com.solum.xplain.core.company.value.IpvDataGroupVo} and {@link
 * com.solum.xplain.core.company.value.ProvidersVo} respectively as their output. The rulesets take
 * all settings at the state date, for all companies and entities, and so can be cached and run
 * against any trade. TODO: the way of storing settings is changing so this implementation will need
 * to be updated accordingly (but not consumers of the service)
 */
@Service
@RequiredArgsConstructor
public class IpvSettingsRuleService {
  private final CompanyIpvSettingsRepository repository;
  private final CompanyLegalEntityIpvSettingsRepository legalEntityIpvSettingsRepository;
  private final CachingTradeLevelOverrideService tradeLevelOverrideService;
  private final IpvDataGroupRepository ipvDataGroupRepository;
  private final IpvDataGroupRuleMapper ipvDataGroupRuleMapper;
  private final IpvProvidersRuleMapper ipvProvidersRuleMapper;
  private final RulesService<Rule, Rules> rulesService;

  /**
   * Return a ruleset to determine valuation data group for a trade, as of a particular state date.
   * The ruleset will expect {@link RulesService#execute(Object, Object, Class)} to be called with
   * {@link com.solum.xplain.core.company.mapper.IpvRuleFacts} and a return type of {@link
   * com.solum.xplain.core.company.value.IpvDataGroupVo}.
   *
   * @param stateDate the state date used to identify the settings in force
   * @return a ruleset to determine valuation data group for a trade
   */
  public Rules getAllIpvDataGroupRules(BitemporalDate stateDate) {
    List<CompanyIpvSettings> allCompanySettings = repository.findAllLatestVersions(stateDate);
    List<CompanyLegalEntityIpvSettings> allEntitySettings =
        legalEntityIpvSettingsRepository.findAllLatestVersions(stateDate);

    Set<Rule> rules =
        Stream.concat(
                allCompanySettings.stream()
                    .filter(s -> s.getProducts() != null)
                    .map(ipvDataGroupRuleMapper::toIpvDataGroupRule),
                allEntitySettings.stream()
                    .filter(s -> s.getSettingsType() == CompanySettingsType.BESPOKE)
                    .filter(s -> s.getProducts() != null)
                    .map(ipvDataGroupRuleMapper::toIpvDataGroupRule))
            .collect(Collectors.toSet());

    var tradeLevelOverrideDataGroupRule =
        new MatchingTradeLevelOverrideDataGroupRule(
            tradeLevelOverrideService, ipvDataGroupRepository, stateDate);
    rules.add(tradeLevelOverrideDataGroupRule);

    return rulesService.createRuleSet(Set.of(rulesService.oneOf(rules)));
  }

  /**
   * Return a ruleset to determine providers for a trade, as of a particular state date. The ruleset
   * will expect {@link RulesService#execute(Object, Object, Class)} to be called with {@link
   * com.solum.xplain.core.company.mapper.IpvRuleFacts} and a return type of {@link
   * com.solum.xplain.core.company.value.ProvidersVo}.
   *
   * @param stateDate the state date used to identify the settings in force
   * @return a ruleset to determine providers for a trade
   */
  public Rules getAllIpvProvidersRules(BitemporalDate stateDate) {
    List<CompanyIpvSettings> allCompanySettings = repository.findAllLatestVersions(stateDate);
    List<CompanyLegalEntityIpvSettings> allEntitySettings =
        legalEntityIpvSettingsRepository.findAllLatestVersions(stateDate);

    Set<Rule> rules =
        Stream.concat(
                allCompanySettings.stream()
                    .filter(s -> s.getProducts() != null)
                    .map(ipvProvidersRuleMapper::toProvidersRule),
                allEntitySettings.stream()
                    .filter(s -> s.getSettingsType() == CompanySettingsType.BESPOKE)
                    .filter(s -> s.getProducts() != null)
                    .map(ipvProvidersRuleMapper::toProvidersRule))
            .collect(Collectors.toSet());

    var tradeLevelOverrideProvidersRule =
        new MatchingTradeLevelOverrideProvidersRule(tradeLevelOverrideService, stateDate);
    rules.add(tradeLevelOverrideProvidersRule);
    return rulesService.createRuleSet(Set.of(rulesService.oneOf(rules)));
  }
}
