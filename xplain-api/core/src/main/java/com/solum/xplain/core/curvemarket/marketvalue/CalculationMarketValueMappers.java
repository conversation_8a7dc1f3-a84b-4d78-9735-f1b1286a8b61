package com.solum.xplain.core.curvemarket.marketvalue;

import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.ASK;
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.BID;
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.MID;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toList;

import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType;
import com.solum.xplain.core.mdvalue.value.MarketDataValueFlatView;
import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public class CalculationMarketValueMappers {

  private CalculationMarketValueMappers() {}

  public static CalculationMarketValueMapperFunction<CalculationMarketValueFullView>
      toMarketValueFullViewPrimary() {
    return (key, provider, values, priceType) ->
        values.stream()
            .filter(p -> p.getProvider().equals(provider.getPrimary()))
            .collect(collectingAndThen(toList(), v -> toFullView(key, v, priceType)));
  }

  public static CalculationMarketValueMapperFunction<CalculationMarketValueView>
      toMarketValueViewPrimary() {
    return (key, provider, values, priceType) ->
        values.stream()
            .filter(p -> p.getProvider().equals(provider.getPrimary()))
            .collect(collectingAndThen(toList(), r -> toView(key, r, priceType)));
  }

  public static CalculationMarketValueMapperFunction<CalculationMarketValueFullView>
      toMarketValueFullViewSecondary() {
    return (key, provider, values, priceType) ->
        values.stream()
            .filter(p -> p.getProvider().equals(provider.getSecondary()))
            .collect(collectingAndThen(toList(), v -> toFullView(key, v, priceType)));
  }

  public static CalculationMarketValueMapperFunction<CalculationMarketValueView>
      toMarketValueViewSecondary() {
    return (key, provider, values, priceType) ->
        values.stream()
            .filter(p -> p.getProvider().equals(provider.getSecondary()))
            .collect(collectingAndThen(toList(), r -> toView(key, r, priceType)));
  }

  private static Optional<CalculationMarketValueView> toView(
      String key, List<MarketDataValueFlatView> values, InstrumentPriceType priceType) {
    return values.stream()
        .findFirst()
        .map(
            v -> {
              var view = new CalculationMarketValueView();
              return fillViewCommonValues(view, key, values, priceType);
            });
  }

  private static Optional<CalculationMarketValueFullView> toFullView(
      String key, List<MarketDataValueFlatView> values, InstrumentPriceType priceType) {
    return values.stream()
        .findFirst()
        .map(
            v -> {
              var view = new CalculationMarketValueFullView();
              view.setId(v.getId());
              view.setProvider(v.getProvider());
              view.setTicker(v.getTicker());
              return fillViewCommonValues(view, key, values, priceType);
            });
  }

  private static <T extends CalculationMarketValueView> T fillViewCommonValues(
      T view, String key, List<MarketDataValueFlatView> values, InstrumentPriceType priceType) {
    var askValue = resolveValue(values, ASK);
    var midValue = resolveValue(values, MID);
    var bidValue = resolveValue(values, BID);
    view.setKey(key);
    view.setPriceType(priceType);
    priceType.calculateRequiredPrice(askValue, midValue, bidValue).ifPresent(view::setValue);
    view.setAskValue(askValue);
    view.setMidValue(midValue);
    view.setBidValue(bidValue);
    return view;
  }

  private static BigDecimal resolveValue(
      List<MarketDataValueFlatView> values, ValueBidAskType type) {
    return values.stream()
        .filter(v -> type.equals(v.getBidAsk()))
        .findFirst()
        .map(MarketDataValueFlatView::getValue)
        .orElse(null);
  }
}
