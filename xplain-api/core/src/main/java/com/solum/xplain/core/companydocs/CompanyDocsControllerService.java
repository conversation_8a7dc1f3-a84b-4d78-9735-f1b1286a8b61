package com.solum.xplain.core.companydocs;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.common.team.UserTeamEntity;
import com.solum.xplain.core.company.repository.CompanyRepository;
import com.solum.xplain.core.company.value.CompanyView;
import com.solum.xplain.core.companydocs.value.CompanyDocForm;
import com.solum.xplain.core.companydocs.value.CompanyDocView;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class CompanyDocsControllerService {

  private final CompanyRepository companyRepository;
  private final CompanyDocsRepository repository;
  private final AuthenticationContext authenticationContext;

  public Either<ErrorItem, EntityId> saveDocument(String companyId, CompanyDocForm form) {
    return company(companyId).flatMap(c -> repository.saveDocument(c.getId(), form));
  }

  public Either<ErrorItem, List<CompanyDocView>> companyDocuments(String companyId) {
    return company(companyId).map(CompanyView::getId).map(repository::companyDocuments);
  }

  public Either<ErrorItem, FileResponseEntity> exportDocument(String companyId, String documentId) {
    return company(companyId)
        .map(CompanyView::getId)
        .flatMap(id -> repository.exportDocument(companyId, documentId));
  }

  public Either<ErrorItem, EntityId> archiveDocument(String companyId, String documentId) {
    return company(companyId)
        .map(CompanyView::getId)
        .flatMap(id -> repository.archiveDocument(id, documentId));
  }

  private Either<ErrorItem, CompanyView> company(String companyId) {
    return companyRepository
        .userCompanyView(authenticationContext.currentUser(), companyId)
        .map(UserTeamEntity::getView);
  }
}
