package com.solum.xplain.core.classifiers.csv;

import static com.solum.xplain.core.common.csv.CsvColumn.textObject;

import com.opengamma.strata.basics.index.Index;
import com.opengamma.strata.product.swap.type.FixedIborSwapConvention;
import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.extensions.index.OffshoreIndices;
import java.util.List;
import org.springframework.core.io.ByteArrayResource;

public class FixedIborSwapConventionMapper extends CsvMapper<FixedIborSwapConvention> {

  private static final List<CsvColumn<FixedIborSwapConvention>> COLUMNS =
      List.of(
          textObject("name", FixedIborSwapConvention::getName),
          textObject("spotDateOffset", FixedIborSwapConvention::getSpotDateOffset),
          textObject("fixedLeg.currency", x -> x.getFixedLeg().getCurrency()),
          textObject("fixedLeg.dayCount", x -> x.getFixedLeg().getDayCount()),
          textObject("fixedLeg.accrualFreq", x -> x.getFixedLeg().getAccrualFrequency()),
          textObject("fixedLeg.accrual", x -> x.getFixedLeg().getAccrualMethod()),
          textObject("fixedLeg.accrualBDA", x -> x.getFixedLeg().getAccrualBusinessDayAdjustment()),
          textObject(
              "fixedLeg.startDateBDA", x -> x.getFixedLeg().getStartDateBusinessDayAdjustment()),
          textObject("fixedLeg.endDateBDA", x -> x.getFixedLeg().getEndDateBusinessDayAdjustment()),
          textObject("fixedLeg.stubConv", x -> x.getFixedLeg().getStubConvention()),
          textObject("fixedLeg.rollConv", x -> x.getFixedLeg().getRollConvention()),
          textObject("fixedLeg.payFreq", x -> x.getFixedLeg().getPaymentFrequency()),
          textObject("fixedLeg.payDateOffset", x -> x.getFixedLeg().getPaymentDateOffset()),
          textObject("fixedLeg.compounding", x -> x.getFixedLeg().getCompoundingMethod()),
          textObject("floatingLeg.index", FixedIborSwapConventionMapper::resolveIndex),
          textObject("floatingLeg.currency", x -> x.getFloatingLeg().getCurrency()),
          textObject("floatingLeg.dayCount", x -> x.getFloatingLeg().getDayCount()),
          textObject("floatingLeg.accrualFreq", x -> x.getFloatingLeg().getAccrualFrequency()),
          textObject(
              "floatingLeg.accrualBDA", x -> x.getFloatingLeg().getAccrualBusinessDayAdjustment()),
          textObject(
              "floatingLeg.endDateBDA", x -> x.getFloatingLeg().getEndDateBusinessDayAdjustment()),
          textObject("floatingLeg.stubConv", x -> x.getFloatingLeg().getStubConvention()),
          textObject("floatingLeg.rollConv", x -> x.getFloatingLeg().getRollConvention()),
          textObject("floatingLeg.fixDateOffset", x -> x.getFloatingLeg().getFixingDateOffset()),
          textObject("floatingLeg.fixRelativeTo", x -> x.getFloatingLeg().getFixingRelativeTo()),
          textObject("floatingLeg.payFreq", x -> x.getFloatingLeg().getPaymentFrequency()),
          textObject("floatingLeg.payDateOffset", x -> x.getFloatingLeg().getPaymentDateOffset()),
          textObject("floatingLeg.compounding", x -> x.getFloatingLeg().getCompoundingMethod()),
          textObject("floatingLeg.notionalExc", x -> x.getFloatingLeg().isNotionalExchange()));

  private FixedIborSwapConventionMapper() {
    super(COLUMNS, null);
  }

  public static ByteArrayResource fixedIborSwapConventionsCsv() {
    return new ConventionalTradeConventionExporter<>(
            new FixedIborSwapConventionMapper(), FixedIborSwapConvention.class)
        .export();
  }

  private static Index resolveIndex(FixedIborSwapConvention convention) {
    var index = convention.getFloatingLeg().getIndex();
    if (OffshoreIndices.isOffshore(index)) {
      return OffshoreIndices.fromOffshoreIbor(index).orElse(index);
    }
    return index;
  }
}
