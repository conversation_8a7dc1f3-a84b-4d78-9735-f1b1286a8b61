package com.solum.xplain.core.curvemarket.pricetype;

import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType.BID_PRICE;
import static java.util.Optional.ofNullable;

import org.mapstruct.Mapper;

@Mapper
public interface InstrumentPriceRequirementsMapper {

  default InstrumentPriceRequirements fromForm(InstrumentPriceRequirementsForm form) {
    if (form == null) {
      return InstrumentPriceRequirements.bidRequirements();
    }
    return new InstrumentPriceRequirements(
        ofNullable(form.getCurvesPriceType()).orElse(BID_PRICE),
        ofNullable(form.getDscCurvesPriceType()).orElse(BID_PRICE),
        ofNullable(form.getFxRatesPriceType()).orElse(BID_PRICE),
        ofNullable(form.getVolsPriceType()).orElse(BID_PRICE),
        ofNullable(form.getVolsSkewsPriceType()).orElse(BID_PRICE));
  }
}
