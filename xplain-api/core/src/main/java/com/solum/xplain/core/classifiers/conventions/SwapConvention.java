package com.solum.xplain.core.classifiers.conventions;

import com.solum.xplain.core.portfolio.value.CalculationType;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.Data;

@Data
public class SwapConvention {

  private String name;
  private String conventionName;
  private String spotDateOffset;

  private Leg payLeg;
  private Leg receiveLeg;

  public Optional<Leg> legByType(CalculationType type) {
    return Stream.of(payLeg, receiveLeg).filter(t -> t.getCalculationType() == type).findFirst();
  }
}
