package com.solum.xplain.core.curvemarket.pricetype;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class InstrumentPriceRequirementsForm {

  @Schema(
      description = "Price type for projection curves",
      nullable = true,
      defaultValue = "BID_PRICE")
  private InstrumentPriceType curvesPriceType;

  @Schema(
      description = "Price type for discount curves",
      nullable = true,
      defaultValue = "BID_PRICE")
  private InstrumentPriceType dscCurvesPriceType;

  @Schema(description = "Price type for fx rates", nullable = true, defaultValue = "BID_PRICE")
  private InstrumentPriceType fxRatesPriceType;

  @Schema(description = "Price type for optionalities", nullable = true, defaultValue = "BID_PRICE")
  private InstrumentPriceType volsPriceType;

  @Schema(description = "Price type for skews", nullable = true, defaultValue = "BID_PRICE")
  private InstrumentPriceType volsSkewsPriceType;
}
