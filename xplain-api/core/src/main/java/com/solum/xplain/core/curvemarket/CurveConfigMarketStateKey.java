package com.solum.xplain.core.curvemarket;

import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements;
import java.time.LocalDate;
import lombok.Data;
import org.springframework.lang.NonNull;

@Data
public class CurveConfigMarketStateKey {
  @NonNull private final String marketDataGroupId;
  @NonNull private final BitemporalDate stateDate;
  @NonNull private final LocalDate curveDate;
  @NonNull private final String configurationId;
  @NonNull private final MarketDataSourceType marketDataSource;
  @NonNull private final InstrumentPriceRequirements priceRequirements;

  public static CurveConfigMarketStateKey configMarketKey(
      @NonNull String marketDataGroupId,
      @NonNull String curveConfigurationId,
      @NonNull MarketDataSourceType marketDataSourceType,
      @NonNull BitemporalDate stateDate,
      @NonNull LocalDate curveDate,
      @NonNull InstrumentPriceRequirements priceRequirements) {
    return new CurveConfigMarketStateKey(
        marketDataGroupId,
        stateDate,
        curveDate,
        curveConfigurationId,
        marketDataSourceType,
        priceRequirements);
  }
}
