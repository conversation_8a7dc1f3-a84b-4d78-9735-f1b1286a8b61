package com.solum.xplain.xm.excmngmt.processipv.view.viewconfig;

import static com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeOverlayResultView.FieldName.*;

import com.solum.xplain.core.viewconfig.provider.AbstractColumnsBuilder;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeResultBreakView;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeResultResolutionView;
import com.solum.xplain.xm.excmngmt.processipv.view.TradeResultView;
import java.util.List;
import java.util.Set;
import java.util.function.Predicate;

/** The minimal view includes a predefined subset of fields from the palette. */
class MinimalColumnsBuilder extends AbstractColumnsBuilder {
  private static final Set<String> MINIMAL_POSITION_DETAILS_FIELDS =
      Set.of(
          PORTFOLIO_ID,
          TRADE_ID,
          TYPE,
          TradeResultView.Fields.underlying,
          TradeResultView.Fields.notional,
          TradeResultView.Fields.accountingCost,
          TradeResultView.Fields.dealCost,
          BREAK_AGE);
  private static final Set<String> MINIMAL_VALUATION_DATA_FIELDS =
      Set.of(
          PRIMARY_PREFIX + PV_SUFFIX,
          SECONDARY_PREFIX + PV_SUFFIX,
          PRIMARY_PREFIX + GREEKS_SUFFIX,
          PRIMARY_PREFIX + VEGA_SUFFIX);
  private static final Set<String> MINIMAL_BREAK_TEST_FIELDS =
      Set.of(
          THRESHOLD_LEVEL,
          BREAK_TEST_COLUMNS,
          IpvTradeResultBreakView.Fields.breakTestName,
          TEST_VALUE);
  public static final Set<String> MINIMAL_HIDDEN_FIELDS =
      Set.of(
          IpvTradeResultBreakView.Fields.breakTestName,
          TEST_VALUE,
          PARENT_BREAK_TEST_PROVIDER,
          RESOLUTION_VALUE,
          VERIFICATION_RESOLUTION,
          IpvTradeResultResolutionView.Fields.resolutionComment,
          RESOLUTION_EVIDENCE,
          IpvTradeResultResolutionView.Fields.approvalComment,
          APPROVAL_EVIDENCE);

  public MinimalColumnsBuilder() {
    super(
        List.of(
            builder(MINIMAL_POSITION_DETAILS_FIELDS::contains, "Position Details"),
            builder(MINIMAL_VALUATION_DATA_FIELDS::contains, "Valuation Data"),
            builder(MINIMAL_BREAK_TEST_FIELDS::contains, "Break Test"),
            builder(
                DefaultColumnsBuilder.RESOLUTION_SUMMARY_FIELDS::contains, "Resolution Summary")));
  }

  private static ColumnDefinitionGroupBuilder builder(Predicate<String> matcher, String label) {
    return new ColumnDefinitionGroupBuilder(matcher, label, MINIMAL_HIDDEN_FIELDS::contains);
  }
}
