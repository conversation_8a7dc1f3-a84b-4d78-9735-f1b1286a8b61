package com.solum.xplain.xm.onboarding;

import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED;
import static com.solum.xplain.core.providers.DataProvider.NAV_PROVIDER_CODE;
import static com.solum.xplain.xm.onboarding.entity.ConformityCheckStatus.REQUESTED;
import static com.solum.xplain.xm.onboarding.entity.OnboardingReportItem.ONBOARDING_ITEM_COLLECTION;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.BooleanUtils.isTrue;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.audit.entity.AuditEntry;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.IpvSettingsRuleService;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.LogItem;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository;
import com.solum.xplain.core.rules.RulesService;
import com.solum.xplain.data.valuation.ipv.IpvDataRepository;
import com.solum.xplain.data.valuation.ipv.value.IpvDataProviderValueView;
import com.solum.xplain.xm.dashboards.resolver.PortfoliosFilterBuilder;
import com.solum.xplain.xm.excmngmt.processipv.IpvExceptionManagementResultMapper;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.onboarding.breaks.ValuationConformityCalculationInvocation;
import com.solum.xplain.xm.onboarding.breaks.VendorConformityBreaksService;
import com.solum.xplain.xm.onboarding.entity.OnboardingReport;
import com.solum.xplain.xm.onboarding.entity.OnboardingReportItem;
import com.solum.xplain.xm.onboarding.form.CreateOnboardingReportForm;
import com.solum.xplain.xm.onboarding.settings.TradeCompanySettings;
import com.solum.xplain.xm.onboarding.settings.TradeCompanySettingsResolver;
import io.atlassian.fugue.Either;
import jakarta.inject.Provider;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.bson.types.ObjectId;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.api.Rules;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@NullMarked
public class OnboardingReportCreationService {

  private final PortfolioItemRepository portfolioItemRepository;
  private final ValuationConformityCalculationInvocation valuationConformityCalculationInvocation;
  private final OnboardingReportRepository onboardingReportRepository;
  private final PortfoliosFilterBuilder portfoliosFilterBuilder;
  private final IpvExceptionManagementResultMapper resultMapper;
  private final VendorConformityBreaksService vendorConformityBreaksService;
  private final Provider<IpvDataRepository> ipvDataRepository;
  private final AuditEntryService auditEntryService;
  private final IpvSettingsRuleService ipvSettingsRuleService;
  private final RulesService<Rule, Rules> rulesService;

  public Either<ErrorItem, EntityId> createReport(
      CreateOnboardingReportForm form, BitemporalDate stateDate) {
    var report = new OnboardingReport();
    var portfoliosFilter = portfoliosFilterBuilder.build(form.portfolioIds());
    report.setPortfoliosFilter(portfoliosFilter);
    report.setStateDate(stateDate.getActualDate());
    report.setRecordDate(stateDate.getRecordDate());
    report.setXplainConformity(isTrue(form.xplainConformity()));
    report.setMarketConformity(isTrue(form.marketConformity()));
    report.setVendorConformity(isTrue(form.vendorConformity()));

    var requiredPortfolioItems =
        form.portfolioIds().stream()
            .flatMap(
                portfolioId ->
                    portfolioItemRepository
                        .conformityRequiredTrades(
                            portfolioId,
                            stateDate,
                            report.isXplainConformity(),
                            report.isMarketConformity(),
                            report.isVendorConformity())
                        .stream())
            .toList();
    if (requiredPortfolioItems.isEmpty()) {
      return Either.left(
          OPERATION_NOT_ALLOWED.entity(
              "Selected portfolios have no trades to run onboarding report for."));
    }

    report.setTradesCount(requiredPortfolioItems.size());
    onboardingReportRepository.saveReport(report);

    var tradeSettingsResolver =
        new TradeCompanySettingsResolver(stateDate, ipvSettingsRuleService, rulesService);

    var reportItems =
        requiredPortfolioItems.stream()
            .map(item -> toItem(item, report, tradeSettingsResolver))
            .toList();
    var vendorCheckResults =
        vendorConformityBreaksService.processBreaks(reportItems, tradeSettingsResolver, stateDate);
    onboardingReportRepository.saveReportItems(reportItems);

    var valuationsInvocationResults =
        valuationConformityCalculationInvocation.invokeValuations(reportItems, report);

    var logs = ImmutableList.<LogItem>builder();
    logs.addAll(vendorCheckResults);
    logs.addAll(valuationsInvocationResults);

    if (onboardingReportRepository.allReportItemsProcessed(report.getId())) {
      onboardingReportRepository.updateReportCompleted(report.getId());
    }
    storeAudit(report.getId(), logs.build());
    return Either.right(EntityId.entityId(report.getId()));
  }

  private OnboardingReportItem toItem(
      PortfolioItem item,
      OnboardingReport report,
      TradeCompanySettingsResolver tradeSettingsResolver) {
    var trade = resultMapper.fromPortfolioItem(item);
    var reportItem = new OnboardingReportItem();
    reportItem.setReportId(report.getId());
    reportItem.setTrade(trade);
    var onboardingDetails = item.getOnboardingDetails();
    if (report.isXplainConformity() && isTrue(onboardingDetails.getXplainCostCheck())) {
      reportItem.setXplainCheckStatus(REQUESTED);
    }
    if (report.isMarketConformity() && isTrue(onboardingDetails.getMarketConfCheck())) {
      reportItem.setMarketCheckStatus(REQUESTED);
    }
    if (report.isVendorConformity() && isTrue(onboardingDetails.getVendorCheck())) {
      reportItem.setVendorCheckStatus(REQUESTED);
    }
    resolveNav(trade, trade.getTradeDetails().getInfo().getTradeDate(), tradeSettingsResolver)
        .ifPresent(reportItem::setNavOnTradeDate);
    ofNullable(onboardingDetails.getVendorOnboardingDate())
        .flatMap(date -> resolveNav(trade, date, tradeSettingsResolver))
        .ifPresent(reportItem::setNavOnVendorOnboardingDate);
    return reportItem;
  }

  private void storeAudit(ObjectId reportId, List<LogItem> logs) {
    var auditEntry =
        AuditEntry.of(
            ONBOARDING_ITEM_COLLECTION, "Onboarding report created", reportId.toHexString());
    auditEntryService.newEntryWithLogs(auditEntry, logs);
  }

  private Optional<BigDecimal> resolveNav(
      Trade trade, LocalDate date, TradeCompanySettingsResolver tradeSettingsResolver) {
    return tradeSettingsResolver
        .tradeSettings(trade)
        .toOptional()
        .map(TradeCompanySettings::groupId)
        .map(
            g -> ipvDataRepository.get().getValueAtDate(g, trade.getKey(), NAV_PROVIDER_CODE, date))
        .flatMap(Either::toOptional)
        .map(IpvDataProviderValueView::getValue);
  }
}
