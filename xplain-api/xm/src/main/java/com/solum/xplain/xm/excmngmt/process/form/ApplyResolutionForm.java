package com.solum.xplain.xm.excmngmt.process.form;

import com.solum.xplain.xm.excmngmt.process.enums.InstrumentResultResolutionType;
import com.solum.xplain.xm.excmngmt.process.form.validation.ValidResolutionTypes;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.group.GroupSequenceProvider;

@Data
@AllArgsConstructor
@NoArgsConstructor
@GroupSequenceProvider(ApplyResolutionFormGroupProvider.class)
@ValidResolutionTypes
public class ApplyResolutionForm {

  @NotNull @Valid private InstrumentFilterForm filter;

  @NotNull @Valid private ResolutionForm overallResolution;

  @NotNull private List<String> selectedIds;

  public Set<InstrumentResultResolutionType> resolutionTypes() {
    if (overallResolution != null) {
      return overallResolution.resolutionType() == null
          ? Set.of()
          : Set.of(overallResolution.resolutionType());
    }
    return Set.of();
  }
}
