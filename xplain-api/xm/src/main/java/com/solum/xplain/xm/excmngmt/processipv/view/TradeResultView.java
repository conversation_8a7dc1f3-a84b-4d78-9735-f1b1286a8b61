package com.solum.xplain.xm.excmngmt.processipv.view;

import com.solum.xplain.core.classifiers.ClassifiersControllerService;
import com.solum.xplain.core.portfolio.trade.CustomTradeField;
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier;
import com.solum.xplain.core.product.ProductGroup;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewField;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewIgnore;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import com.solum.xplain.extensions.enums.CreditSector;
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeOverlayResultView.FieldName;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class TradeResultView {

  @ConfigurableViewIgnore private String key;

  @ConfigurableViewField(
      value = FieldName.CCY,
      enumClassifier = ClassifiersControllerService.CURRENCY_CLASSIFIER)
  @ConfigurableViewQuery(sortable = true)
  private String currency;

  @ConfigurableViewField(enumClassifier = ClassifiersControllerService.CURRENCY_PAIR_CLASSIFIER)
  @ConfigurableViewQuery(sortable = true)
  private String currencyPair;

  // TradeInfo fields
  @ConfigurableViewField(FieldName.TYPE)
  @ConfigurableViewQuery(sortable = true)
  private ProductType tradeInfoTradeType;

  @ConfigurableViewQuery(sortable = true)
  private ProductGroup tradeInfoTradeTypeGroup;

  @ConfigurableViewField(FieldName.START_DATE)
  @ConfigurableViewQuery(sortable = true)
  private LocalDate tradeInfoStartDate;

  @ConfigurableViewField(FieldName.END_DATE)
  @ConfigurableViewQuery(sortable = true)
  private LocalDate tradeInfoEndDate;

  @ConfigurableViewField(FieldName.EXPIRY)
  @ConfigurableViewQuery(sortable = true)
  private LocalDate tradeInfoExpiryDate;

  @ConfigurableViewQuery(sortable = true)
  private String underlying;

  @ConfigurableViewQuery(sortable = true)
  private Double notional;

  @ConfigurableViewQuery(sortable = true)
  private Double accountingCost;

  @ConfigurableViewQuery(sortable = true)
  private Double dealCost;

  @ConfigurableViewIgnore private String portfolioId;

  @ConfigurableViewField(FieldName.PORTFOLIO_ID)
  @ConfigurableViewQuery(sortable = true)
  private String portfolioExternalId;

  @ConfigurableViewField(FieldName.COMPANY_ID)
  @ConfigurableViewQuery(sortable = true)
  private String externalCompanyId;

  @ConfigurableViewField(FieldName.ENTITY_ID)
  @ConfigurableViewQuery(sortable = true)
  private String externalEntityId;

  @ConfigurableViewIgnore private String entityId;

  @ConfigurableViewField(FieldName.TRADE_ID)
  @ConfigurableViewQuery(sortable = true)
  private String tradeInfoExternalTradeId;

  private List<ExternalIdentifier> tradeInfoExternalIdentifiers;

  private List<CustomTradeField> tradeInfoCustomFields;

  @ConfigurableViewQuery(sortable = true)
  private CreditSector creditSector;
}
