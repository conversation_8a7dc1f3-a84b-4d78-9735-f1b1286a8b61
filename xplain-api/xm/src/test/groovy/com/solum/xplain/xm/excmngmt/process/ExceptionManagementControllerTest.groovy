package com.solum.xplain.xm.excmngmt.process

import static com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus.IN_PRELIMINARY
import static groovy.json.JsonOutput.toJson
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.value.EntityNameView
import com.solum.xplain.core.market.repository.MarketDataGroupRepository
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.shared.utils.filter.TableFilter
import com.solum.xplain.xm.excmngmt.form.ApplyVerificationForm
import com.solum.xplain.xm.excmngmt.process.data.ExceptionManagementResult
import com.solum.xplain.xm.excmngmt.process.enums.CalculationTestStatus
import com.solum.xplain.xm.excmngmt.process.form.ApplyTaskResolutionForm
import com.solum.xplain.xm.helpers.MockMvcConfiguration
import com.solum.xplain.xm.tasks.entity.TaskExecution
import com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType
import com.solum.xplain.xm.tasks.repository.TaskExecutionRepository
import com.solum.xplain.xm.workflow.view.CallActivityProgressView
import java.nio.charset.StandardCharsets
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.security.core.Authentication
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@WebMvcTest(controllers = [ExceptionManagementController])
@MockMvcConfiguration
class ExceptionManagementControllerTest extends Specification {

  @SpringBean
  ExceptionManagementControllerService service = Mock()
  @SpringBean
  AuthenticationContext userRepository = Mock()
  @SpringBean
  ExceptionManagementCalculationRepository repository = Mock()
  @SpringBean
  TaskExecutionRepository executionRepository = Mock()
  @SpringBean
  RequestPathVariablesSupport variablesSupport = new RequestPathVariablesSupport()
  @SpringBean
  MarketDataGroupRepository marketDataGroupRepository = Mock()

  @Autowired
  ObjectMapper objectMapper
  @Autowired
  MockMvc mockMvc

  @WithMockUser
  @Unroll
  "should perform form (#form) validation with response #code #responseBody when resolution appl"() {
    setup:
    service.applyResolution(
      _ as Authentication,
      _ as ApplyTaskResolutionForm,
      _ as TableFilter,
      null,
      _ as CalculationTestStatus
      ) >> right(List.of(EntityId.entityId("1")))
    variablesSupport.getPathVariable("id") >> "id"
    repository.entity("id") >> right(new ExceptionManagementResult(status: IN_PRELIMINARY))
    marketDataGroupRepository.dataGroupName(_ as String) >> Optional.of(new EntityNameView())
    userRepository.userEither(_ as Authentication) >> right(UserBuilder.user())
    executionRepository.executions(["id"]) >> [new TaskExecution(taskExceptionManagementType: TaskExceptionManagementType.PRELIMINARY)]

    def tableFilterMock = TableFilter.emptyTableFilter()
    def results = mockMvc.perform(multipart("/exception-management/calculations/preliminary/resolution")
      .file(jsonForm(toJson(form)))
      .file(jsonForm(toJson(tableFilterMock)))
      .with(csrf()))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                                                                                     | responseBody                                    | code
    formResOverall({ m -> m })                                                                                               | "id"                                            | 200
    formResOverall({ m -> m.remove("filter") })                                                                              | "NotNull.form.filter"                           | 412
    formResOverall({ m -> m.remove("overallResolution") })                                                                   | "NotNull.form.overallResolution"                | 412
    formResOverall({ m -> m.replace("overallResolution", [newValue: "1", comment: "CCC"]) })                                 | "NotNull.form.overallResolution.resolutionType" | 412
    formResOverall({ m -> m.replace("overallResolution", [resolutionType: "OVERRIDE_USER", comment: "CCC"]) })               | "NotNull.form.overallResolution.newValue"       | 412
    formResOverall({ m -> m.replace("overallResolution", [newValue: "1", resolutionType: "OVERRIDE_USER"]) })                | "NotBlank.form.overallResolution.comment"       | 412
    formResOverall({ m -> m.replace("overallResolution", [newValue: "1", resolutionType: "OVERRIDE_USER", comment: "  "]) }) | "NotBlank.form.overallResolution.comment"       | 412
    formResOverall({ m ->
      m.replace("overallResolution", [
        resolutionType: "SWITCH_TO_SECONDARY",
        comment       : "CCC"
      ])
    })                                                                                                                       | "ValidResolutionTypes"                                             | 412
  }

  private MockMultipartFile jsonForm(String json) {
    return new MockMultipartFile("form", "", MediaType.APPLICATION_JSON_VALUE, json.getBytes(StandardCharsets.UTF_8))
  }

  @WithMockUser
  @Unroll
  "should perform form (#form) validation with response #code #responseBody when overal verification"() {
    setup:
    service.verifyResolutions(
      _ as Authentication,
      _ as ApplyVerificationForm,
      null,
      CalculationTestStatus.IN_OVERLAY,
      TableFilter.emptyTableFilter()
      ) >> right(List.of(EntityId.entityId("id")))
    def results = mockMvc.perform(multipart("/exception-management/calculations/overlay/verify-resolution")
      .file(jsonForm(toJson(form)))
      .with(csrf()))
      .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                                                     | responseBody                                | code
    formVerificationOverall({ m -> m })                                                      | "id"                                        | 200
    formVerificationOverall({ m -> m.remove("taskIds") })                                    | "NotEmpty.form.taskIds"                     | 412
    formVerificationOverall({ m -> m.put("taskIds", []) })                                   | "NotEmpty.form.taskIds"                     | 412
    formVerificationOverall({ m -> m.remove("selectedIds") })                                | "NotNull.form.selectedIds"                  | 412
    formVerificationOverall({ m -> m.replace("overallVerification", [status: "VERIFIED"]) }) | "NotBlank.form.overallVerification.comment" | 412
  }

  def "should return workflow progress for overlay"() {
    setup:
    1 * service.progress("key") >> new CallActivityProgressView(totalCount: 10)

    when:
    def results = mockMvc.perform(get("/exception-management/calculations/overlay/progress?key=key"))
      .andReturn()

    then:
    with (results.getResponse()) {
      getStatus() == 200
      with (objectMapper.readValue(getContentAsByteArray(), CallActivityProgressView)) {
        totalCount == 10
      }
    }
  }

  def "should return workflow progress for preliminary"() {
    setup:
    1 * service.progress("key") >> new CallActivityProgressView(totalCount: 10)

    when:
    def results = mockMvc.perform(get("/exception-management/calculations/preliminary/progress?key=key"))
      .andReturn()

    then:
    with (results.getResponse()) {
      getStatus() == 200
      with (objectMapper.readValue(getContentAsByteArray(), CallActivityProgressView)) {
        totalCount == 10
      }
    }
  }

  static def form(Closure c) {
    [
      curveDate        : "2020-01-02",
      stateDate        : "2020-01-03",
      marketDataGroupId: "marketDataId"
    ].with(true, c)
  }

  static def formResOverall(Closure c) {
    [
      taskIds          : ["id"],
      selectedIds      : [],
      filter           : [
        curveConfigurations: [],
        providers          : [],
        breakingTests      : [],
        assetFilter        : [
          "assetClasses" : [],
          "rateCcys"     : [],
          "irInstruments": [],
          "creditSectors": [],
          "fxPairs"      : [],
        ],
        curves             : [],
        bidAskTypes        : [],
      ],
      overallResolution: [
        resolutionType: "OVERRIDE_USER",
        newValue      : "1",
        comment       : "CCC"
      ]
    ].with(true, c)
  }

  static def formVerificationOverall(Closure c) {
    [
      taskIds            : ["id"],
      overallVerification: [
        status : "VERIFIED",
        comment: "CCC"
      ],
      selectedIds        : []
    ].with(true, c)
  }
}
