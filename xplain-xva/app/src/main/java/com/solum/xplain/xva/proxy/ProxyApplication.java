package com.solum.xplain.xva.proxy;

import com.solum.xplain.xva.proxy.config.S3Properties;
import com.solum.xplain.xva.proxy.config.XvaProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.web.servlet.DispatcherServlet;

@SpringBootApplication
@EnableConfigurationProperties({XvaProperties.class, S3Properties.class})
public class ProxyApplication {
  private static final String SPRING_ACTIVE_PROFILES = "spring.profiles.active";
  private static final String DEV_PROFILE = "dev";

  public static void main(String[] args) {
    if (System.getProperty(SPRING_ACTIVE_PROFILES) == null
        && System.getenv("SPRING_PROFILES_ACTIVE") == null) {
      System.setProperty(SPRING_ACTIVE_PROFILES, DEV_PROFILE);
    }
    ConfigurableApplicationContext context = SpringApplication.run(ProxyApplication.class, args);
    DispatcherServlet dispatcherServlet = context.getBean(DispatcherServlet.class);
    // NB Only use this if you are using non-pooling thread executors.
    dispatcherServlet.setThreadContextInheritable(true);
  }
}
