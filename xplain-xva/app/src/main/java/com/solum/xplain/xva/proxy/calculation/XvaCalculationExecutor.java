package com.solum.xplain.xva.proxy.calculation;

import static org.slf4j.LoggerFactory.getLogger;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.solum.xplain.xva.proxy.integration.S3DataStore;
import com.solum.xplain.xva.proxy.messages.XvaCalculationRequest;
import com.solum.xplain.xva.proxy.messages.XvaCalculationResponse;
import com.solum.xplain.xva.proxy.messages.XvaControlFile;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import org.jspecify.annotations.NullMarked;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@NullMarked
public class XvaCalculationExecutor {

  private static final Logger LOG = getLogger(XvaCalculationExecutor.class);

  private final S3DataStore dataStore;
  private final Path tempDir;
  private final JuliaScriptInvoker juliaScriptInvoker;
  private final ObjectMapper objectMapper;

  public XvaCalculationExecutor(
      S3DataStore dataStore,
      @Value("${app.xva-proxy.temp-dir}") Path tempDir,
      JuliaScriptInvoker juliaScriptInvoker,
      ObjectMapper objectMapper) {
    this.dataStore = dataStore;
    this.tempDir = tempDir;
    this.juliaScriptInvoker = juliaScriptInvoker;
    this.objectMapper = objectMapper;
  }

  public XvaCalculationResponse processXvaControlFile(XvaCalculationRequest request) {
    try {
      var controlMessage = request.getControlFile();
      LOG.info("Received: {}", controlMessage);

      Path tempFilesDir = Files.createDirectories(tempDir.resolve(request.getCalculationId()));

      LOG.debug("XVA working dir {}", tempFilesDir);
      Path marketFile = tempFilesDir.resolve("marketFile.json");
      Path tradesFile = tempFilesDir.resolve("trades.csv");
      Path resultsFile = tempFilesDir.resolve("results.json");
      Path controlFile = tempFilesDir.resolve("controlFile.json");
      Path outputModelFile = tempFilesDir.resolve("model.jls");

      dataStore.downloadFile(controlMessage.getMarketFile(), marketFile);
      dataStore.downloadFile(controlMessage.getTradeFile(), tradesFile);
      Path destinationDir = Path.of(controlMessage.getMarketFile()).getParent();

      XvaControlFile localControl =
          controlMessage.toBuilder()
              .marketFile(tempFilesDir.relativize(marketFile).toString())
              .tradeFile(tempFilesDir.relativize(tradesFile).toString())
              .resultsFile(tempFilesDir.relativize(resultsFile).toString())
              .outputModelFile(tempFilesDir.relativize(outputModelFile).toString())
              .build();
      objectMapper.writerWithDefaultPrettyPrinter().writeValue(controlFile.toFile(), localControl);
      dataStore.uploadFile(
          destinationDir.resolve(controlFile.getFileName()).toString(), controlFile);

      juliaScriptInvoker.invokeScript(controlFile, resultsFile);

      dataStore.uploadFile(
          destinationDir.resolve(resultsFile.getFileName()).toString(), resultsFile);
      dataStore.uploadFile(
          destinationDir.resolve(outputModelFile.getFileName()).toString(), outputModelFile);

      LOG.info("Finished: {}", controlMessage);
      return XvaCalculationResponse.success(request.getCalculationId(), controlMessage);
    } catch (IOException | RuntimeException ex) {
      LOG.info("Error while processing message:", ex);
      return XvaCalculationResponse.error(request.getCalculationId(), ex.getMessage());
    } catch (InterruptedException ex) {
      LOG.info("Interrupted exception:", ex);
      Thread.currentThread().interrupt();
      return XvaCalculationResponse.error(request.getCalculationId(), ex.getMessage());
    }
  }
}
