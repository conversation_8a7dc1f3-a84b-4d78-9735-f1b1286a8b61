package com.solum.xplain.xva.proxy.messages;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

@Data
public class SwaptionVolatilitiesValue {

  @JsonProperty("FixedFrequency")
  private final int fixedFrequency;

  @JsonProperty("FloatingFrequency")
  private final int floatingFrequency;

  @JsonProperty("FixedDCT")
  private final String fixedDCT;

  @JsonProperty("FloatingDCT")
  private final String floatingDCT;

  private final List<String> xlabels;
  private final List<String> ylabels;

  private final double[][] data;
}
