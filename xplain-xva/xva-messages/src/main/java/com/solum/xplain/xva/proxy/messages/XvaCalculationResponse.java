package com.solum.xplain.xva.proxy.messages;

import lombok.Data;

@Data
public class XvaCalculationResponse {

  private final String calculationId;
  private final String errorMessage;
  private final XvaControlFile controlFile;

  public static XvaCalculationResponse error(String calculationId, String errorMessage) {
    return new XvaCalculationResponse(calculationId, errorMessage, null);
  }

  public static XvaCalculationResponse success(String calculationId, XvaControlFile controlFile) {
    return new XvaCalculationResponse(calculationId, null, controlFile);
  }
}
