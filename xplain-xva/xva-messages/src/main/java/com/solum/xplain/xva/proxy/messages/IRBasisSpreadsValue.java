package com.solum.xplain.xva.proxy.messages;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

@Data
public class IRBasisSpreadsValue {
  @JsonProperty("Tenors")
  private final List<String> tenors;

  @JsonProperty("Rates")
  private final List<Double> rates;

  @JsonProperty("IBORFreq")
  private final List<Integer> iborFreq;

  @JsonProperty("IBORDCT")
  private final List<String> iborDct;

  @JsonProperty("RFRFreq")
  private final List<Integer> rfrFreq;

  @JsonProperty("RFRDCT")
  private final List<String> rfrDct;

  @JsonProperty("SpreadIsOn")
  private final List<String> spreadIsOn;
}
