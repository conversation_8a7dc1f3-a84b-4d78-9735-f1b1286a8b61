package com.solum.xplain.xva.proxy.messages;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

public class XvaMarketFile implements Map<String, Object> {
  private final Map<String, Object> data;

  public XvaMarketFile(Map<String, Object> data) {
    this.data = data;
  }

  @Override
  public int size() {
    return data.size();
  }

  @Override
  public boolean isEmpty() {
    return data.isEmpty();
  }

  @Override
  public boolean containsKey(Object key) {
    return data.containsKey(key);
  }

  @Override
  public boolean containsValue(Object value) {
    return data.containsValue(value);
  }

  @Override
  public Object get(Object key) {
    return data.get(key);
  }

  @Override
  public Object put(String key, Object value) {
    return data.put(key, value);
  }

  @Override
  public Object remove(Object key) {
    return data.remove(key);
  }

  @Override
  public void putAll(Map<? extends String, ?> m) {
    data.putAll(m);
  }

  @Override
  public void clear() {
    data.clear();
  }

  @Override
  public Set<String> keySet() {
    return data.keySet();
  }

  @Override
  public Collection<Object> values() {
    return data.values();
  }

  @Override
  public Set<Entry<String, Object>> entrySet() {
    return data.entrySet();
  }

  @Override
  public boolean equals(Object o) {
    return data.equals(o);
  }

  @Override
  public int hashCode() {
    return data.hashCode();
  }
}
