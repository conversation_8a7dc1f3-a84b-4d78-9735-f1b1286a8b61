package com.solum.xplain.xva.proxy.messages;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

@Data
public class XvaPartyResultsFile {
  @JsonProperty("PartyName")
  private List<String> partyName;

  @JsonProperty("PV")
  private List<Double> pv;

  @JsonProperty("FundingPV")
  private List<Double> fundingPV;

  @JsonProperty("PVWhatIf")
  private List<Double> pvWhatIf;

  @JsonProperty("FundingPVWhatIf")
  private List<Double> fundingPVWhatIf;

  @JsonProperty("CVA")
  private List<Double> cva;

  @JsonProperty("DVA")
  private List<Double> dva;

  @JsonProperty("FCA_CP")
  private List<Double> fcaCp;

  @JsonProperty("FBA_CP")
  private List<Double> fbaCp;

  @JsonProperty("FCA_Self")
  private List<Double> fcaSelf;

  @JsonProperty("FBA_Self")
  private List<Double> fbaSelf;

  @JsonProperty("CVAWhatIf")
  private List<Double> cvaWhatIf;

  @JsonProperty("DVAWhatIf")
  private List<Double> dvaWhatIf;

  @JsonProperty("FCAWhatIf_CP")
  private List<Double> fcaWhatIfCp;

  @JsonProperty("FBAWhatIf_CP")
  private List<Double> fbaWhatIfCp;

  @JsonProperty("FCAWhatIf_Self")
  private List<Double> fcaWhatIfSelf;

  @JsonProperty("FBAWhatIf_Self")
  private List<Double> fbaWhatIfSelf;

  @JsonProperty("PVStatus")
  private List<String> pvStatus;

  @JsonProperty("FundingPVStatus")
  private List<String> fundingPVStatus;

  @JsonProperty("PVWhatIfStatus")
  private List<String> pvWhatIfStatus;

  @JsonProperty("FundingPVWhatIfStatus")
  private List<String> fundingPVWhatIfStatus;

  @JsonProperty("CVADVAStatus")
  private List<String> cvaDvaStatus;

  @JsonProperty("NumTrades")
  private List<Integer> numTrades;
}
