package com.solum.xplain.extensions.index

import static com.opengamma.strata.basics.date.BusinessDayConventions.MODIFIED_FOLLOWING

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.date.BusinessDayAdjustment
import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.basics.date.DaysAdjustment
import com.opengamma.strata.basics.date.HolidayCalendarIds
import com.opengamma.strata.basics.date.Tenor
import com.opengamma.strata.basics.date.TenorAdjustment
import com.opengamma.strata.basics.index.FloatingRateIndex
import com.opengamma.strata.basics.index.OvernightIndex
import spock.lang.Specification

class ExtendedOvernightIndicesTest extends Specification {

  def "should correctly load USD-SOFR-3M term index"() {
    when:
    def index = OvernightTermIndex.of("USD-SOFR-3M")

    then:
    index.getName() == "USD-SOFR-3M"
    index.getCurrency() == Currency.USD
    index.getFixingCalendar() == HolidayCalendarIds.USGS
    index.getFixingDateOffset() == DaysAdjustment.ofBusinessDays(0, HolidayCalendarIds.USGS)
    index.getEffectiveDateOffset() == 0
    index.getMaturityDateOffset() == TenorAdjustment.ofLastBusinessDay(
      Tenor.ofMonths(3),
      BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarIds.USGS)
      )
    index.getDayCount() == DayCounts.ACT_360
    index.getTenor() == Tenor.ofMonths(3)
  }

  def "extendedEnum of FloatingRateIndex should include our custom OvernightTermIndices"() {
    when:
    def index = FloatingRateIndex.of("USD-SOFR-6M")

    then:
    index.getName() == "USD-SOFR-6M"
    index.getCurrency() == Currency.USD
  }

  def "extendedEnum of OvernightIndex should include our custom OvernightTermIndex"() {
    when:
    def index = OvernightIndex.of("USD-SOFR-6M")
    def existingIndex = OvernightIndex.of("USD-SOFR")

    then:
    index.getName() == "USD-SOFR-6M"
    index.getCurrency() == Currency.USD
    existingIndex != null
  }
}
