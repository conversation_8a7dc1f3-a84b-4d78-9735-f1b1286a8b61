/*
 * Copyright (C) 2015 - present by OpenGamma Inc. and the OpenGamma group of companies
 *
 * Please see distribution for license.
 */
package com.solum.xplain.extensions.pricers;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.market.explain.ExplainMapBuilder;
import com.opengamma.strata.market.sensitivity.PointSensitivityBuilder;
import com.opengamma.strata.pricer.impl.rate.DispatchingRateComputationFn;
import com.opengamma.strata.pricer.rate.RateComputationFn;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.opengamma.strata.product.rate.RateComputation;
import com.solum.xplain.extensions.index.OvernightTermRateComputation;
import java.time.LocalDate;
import org.jspecify.annotations.NullMarked;

/**
 * Extended dispatching rate computation function that can handle both standard Strata rate
 * computations and custom {@link OvernightTermRateComputation}.
 *
 * <p>This class delegates to the standard {@link DispatchingRateComputationFn} for all standard
 * types and includes custom overnight term computations separately. Due to OG constraints
 */
@NullMarked
public class XplainDispatchingRateComputationFn implements RateComputationFn<RateComputation> {

  private final DispatchingRateComputationFn standardDispatcher;
  private final XplainForwardOvernightTermCompoundedRateComputationFn termOvernightFn;

  public XplainDispatchingRateComputationFn(
      DispatchingRateComputationFn standardDispatcher, ReferenceData referenceData) {
    this.standardDispatcher = standardDispatcher;
    this.termOvernightFn = new XplainForwardOvernightTermCompoundedRateComputationFn(referenceData);
  }

  @Override
  public double rate(
      RateComputation computation, LocalDate startDate, LocalDate endDate, RatesProvider provider) {

    if (computation instanceof OvernightTermRateComputation) {
      return termOvernightFn.rate(
          (OvernightTermRateComputation) computation, startDate, endDate, provider);
    } else {
      return standardDispatcher.rate(computation, startDate, endDate, provider);
    }
  }

  @Override
  public PointSensitivityBuilder rateSensitivity(
      RateComputation computation, LocalDate startDate, LocalDate endDate, RatesProvider provider) {

    if (computation instanceof OvernightTermRateComputation) {
      return termOvernightFn.rateSensitivity(
          (OvernightTermRateComputation) computation, startDate, endDate, provider);
    } else {
      return standardDispatcher.rateSensitivity(computation, startDate, endDate, provider);
    }
  }

  @Override
  public double explainRate(
      RateComputation computation,
      LocalDate startDate,
      LocalDate endDate,
      RatesProvider provider,
      ExplainMapBuilder builder) {

    if (computation instanceof OvernightTermRateComputation) {
      return termOvernightFn.explainRate(
          (OvernightTermRateComputation) computation, startDate, endDate, provider, builder);
    } else {
      return standardDispatcher.explainRate(computation, startDate, endDate, provider, builder);
    }
  }
}
