package com.solum.xplain.extensions.index;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.basics.schedule.Schedule;
import com.opengamma.strata.basics.value.ValueSchedule;
import com.opengamma.strata.collect.array.DoubleArray;
import com.opengamma.strata.product.swap.NegativeRateMethod;
import com.opengamma.strata.product.swap.OvernightAccrualMethod;
import com.opengamma.strata.product.swap.OvernightRateCalculation;
import com.opengamma.strata.product.swap.RateAccrualPeriod;
import com.opengamma.strata.product.swap.RateCalculation;
import com.opengamma.strata.product.swap.SwapLegType;
import java.io.Serializable;
import java.time.LocalDate;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Delegate;
import org.jspecify.annotations.NullMarked;

/**
 * A wrapper around OvernightRateCalculation that adds support for fixingDateOffset, similar to how
 * IborRateCalculation handles fixing date offsets. This is specifically designed for term overnight
 * rates that need custom fixing date offsets.
 */
@NullMarked
@Getter
@EqualsAndHashCode
@RequiredArgsConstructor
@Builder
public final class OvernightTermRateCalculation implements RateCalculation, Serializable {

  @Delegate(types = OvernightRateCalculation.class)
  private final OvernightRateCalculation delegate;

  private final DaysAdjustment fixingDateOffset;

  /** Creates an OvernightTermRateCalculation with the specified parameters. */
  public static OvernightTermRateCalculation of(
      DayCount dayCount,
      OvernightIndex index,
      DaysAdjustment fixingDateOffset,
      ValueSchedule spread,
      OvernightAccrualMethod accrualMethod,
      int rateCutOffDays,
      NegativeRateMethod negativeRateMethod) {

    var delegate =
        OvernightRateCalculation.builder()
            .dayCount(dayCount)
            .index(index)
            .spread(spread)
            .accrualMethod(accrualMethod)
            .rateCutOffDays(rateCutOffDays)
            .negativeRateMethod(negativeRateMethod)
            .build();

    return new OvernightTermRateCalculation(delegate, fixingDateOffset);
  }

  // Override methods that need custom behavior
  @Override
  public SwapLegType getType() {
    return SwapLegType.OTHER;
  }

  @Override
  public ImmutableList<RateAccrualPeriod> createAccrualPeriods(
      Schedule accrualSchedule, Schedule paymentSchedule, ReferenceData refData) {

    DoubleArray resolvedGearings = ValueSchedule.ALWAYS_1.resolveValues(accrualSchedule);
    DoubleArray resolvedSpreads =
        delegate.getSpread().orElse(ValueSchedule.ALWAYS_0).resolveValues(accrualSchedule);

    ImmutableList.Builder<RateAccrualPeriod> accrualPeriods = ImmutableList.builder();

    for (int i = 0; i < accrualSchedule.size(); i++) {
      var period = accrualSchedule.getPeriod(i);
      double yearFraction = period.yearFraction(delegate.getDayCount(), accrualSchedule);

      LocalDate fixingStartDate = fixingDateOffset.resolve(refData).adjust(period.getStartDate());

      var rateComputation =
          OvernightTermRateComputation.of(
              delegate.getIndex(),
              period.getStartDate(),
              period.getEndDate(),
              delegate.getRateCutOffDays(),
              delegate.getAccrualMethod(),
              fixingStartDate,
              refData);

      accrualPeriods.add(
          RateAccrualPeriod.builder()
              .startDate(period.getStartDate())
              .endDate(period.getEndDate())
              .unadjustedStartDate(period.getUnadjustedStartDate())
              .unadjustedEndDate(period.getUnadjustedEndDate())
              .yearFraction(yearFraction)
              .rateComputation(rateComputation)
              .gearing(resolvedGearings.get(i))
              .spread(resolvedSpreads.get(i))
              .negativeRateMethod(delegate.getNegativeRateMethod())
              .build());
    }

    return accrualPeriods.build();
  }
}
