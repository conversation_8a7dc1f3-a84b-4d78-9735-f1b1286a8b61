package com.solum.xplain.extensions.index;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.product.rate.OvernightCompoundedRateComputation;
import com.opengamma.strata.product.rate.RateComputation;
import com.opengamma.strata.product.swap.OvernightAccrualMethod;
import java.io.Serializable;
import java.time.LocalDate;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Delegate;
import org.jspecify.annotations.NullMarked;

/**
 * A rate computation for overnight term rates that includes a custom termFirstFixingDate.
 *
 * <p>This class wraps an OvernightCompoundedRateComputation and adds the termFirstFixingDate that
 * is needed by the pricing logic for proper term rate calculations.
 */
@NullMarked
@Getter
@EqualsAndHashCode
@RequiredArgsConstructor
public final class OvernightTermRateComputation implements RateComputation, Serializable {

  @Delegate(types = OvernightCompoundedRateComputation.class)
  private final OvernightCompoundedRateComputation delegate;

  private final LocalDate termFirstFixingDate;

  /**
   * Creates an OvernightTermRateComputation with the specified parameters.
   *
   * @param index the overnight index
   * @param startDate the start date of the accrual period
   * @param endDate the end date of the accrual period
   * @param rateCutOffDays the rate cut-off days
   * @param accrualMethod the accrual method
   * @param termFirstFixingDate the first fixing date for term rate calculation
   * @param refData the reference data
   * @return the overnight term rate computation
   */
  public static OvernightTermRateComputation of(
      OvernightIndex index,
      LocalDate startDate,
      LocalDate endDate,
      int rateCutOffDays,
      OvernightAccrualMethod accrualMethod,
      LocalDate termFirstFixingDate,
      ReferenceData refData) {

    // Accrual method is currently ignored as we only support compounded for term rates but this may
    // change in the future
    OvernightCompoundedRateComputation delegate =
        OvernightCompoundedRateComputation.of(index, startDate, endDate, rateCutOffDays, refData);

    return new OvernightTermRateComputation(delegate, termFirstFixingDate);
  }

  /**
   * Creates an OvernightTermRateComputation from an existing OvernightCompoundedRateComputation
   * with termFirstFixingDate.
   *
   * @param delegate the underlying overnight compounded rate computation
   * @param termFirstFixingDate the first fixing date for term rate calculation
   * @return the overnight term rate computation
   */
  public static OvernightTermRateComputation of(
      OvernightCompoundedRateComputation delegate, LocalDate termFirstFixingDate) {
    return new OvernightTermRateComputation(delegate, termFirstFixingDate);
  }
}
